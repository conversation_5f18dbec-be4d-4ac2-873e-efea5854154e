var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: !0 });
};

// app/entry.server.tsx
var entry_server_exports = {};
__export(entry_server_exports, {
  default: () => handleRequest
});
import { RemixServer } from "@remix-run/react";
import { renderToString } from "react-dom/server";
import { jsxDEV } from "react/jsx-dev-runtime";
function handleRequest(request, responseStatusCode, responseHeaders, remixContext) {
  let markup = renderToString(
    /* @__PURE__ */ jsxDEV(RemixServer, { context: remixContext, url: request.url }, void 0, !1, {
      fileName: "app/entry.server.tsx",
      lineNumber: 12,
      columnNumber: 5
    }, this)
  );
  return responseHeaders.set("Content-Type", "text/html"), new Response("<!DOCTYPE html>" + markup, {
    status: responseStatusCode,
    headers: responseHeaders
  });
}

// app/root.tsx
var root_exports = {};
__export(root_exports, {
  default: () => App
});
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration
} from "@remix-run/react";
import { jsxDEV as jsxDEV2 } from "react/jsx-dev-runtime";
function App() {
  return /* @__PURE__ */ jsxDEV2("html", { lang: "zh-CN", children: [
    /* @__PURE__ */ jsxDEV2("head", { children: [
      /* @__PURE__ */ jsxDEV2("meta", { charSet: "utf-8" }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 14,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2("meta", { name: "viewport", content: "width=device-width, initial-scale=1" }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 15,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Meta, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 16,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Links, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 17,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2("style", { children: `
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* \u79FB\u52A8\u7AEF\u4F18\u5316 */
          @media (max-width: 768px) {
            body {
              font-size: 14px;
            }

            table {
              font-size: 12px;
            }

            th, td {
              padding: 8px 4px !important;
              min-width: 80px;
            }

            .container {
              padding: 16px;
            }

            h1 {
              font-size: 24px !important;
            }

            h2 {
              font-size: 18px !important;
            }

            h3 {
              font-size: 16px !important;
            }
          }

          /* \u5C0F\u5C4F\u5E55\u4F18\u5316 */
          @media (max-width: 480px) {
            .container {
              padding: 12px;
            }

            table {
              font-size: 11px;
            }

            th, td {
              padding: 6px 3px !important;
              min-width: 70px;
            }
          }

          /* \u6EDA\u52A8\u6761\u6837\u5F0F */
          ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
          }

          ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
          }

          ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
          }

          /* \u8868\u683C\u54CD\u5E94\u5F0F */
          .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
          }

          /* \u6309\u94AE\u89E6\u6478\u4F18\u5316 */
          button {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
          }
        ` }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 18,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/root.tsx",
      lineNumber: 13,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV2("body", { children: [
      /* @__PURE__ */ jsxDEV2(Outlet, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 116,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(ScrollRestoration, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 117,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Scripts, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 118,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(LiveReload, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 119,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/root.tsx",
      lineNumber: 115,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/root.tsx",
    lineNumber: 12,
    columnNumber: 5
  }, this);
}

// app/routes/api.search-property.ts
var api_search_property_exports = {};
__export(api_search_property_exports, {
  action: () => action
});
import { json } from "@remix-run/node";

// app/utils/property-search.server.ts
import { Exa } from "exa-js";
import puppeteer from "puppeteer-extra";
import StealthPlugin from "puppeteer-extra-plugin-stealth";
import OpenAI from "openai";
puppeteer.use(StealthPlugin());
function checkEnvironmentVariables() {
  let missing = ["EXA_API_KEY", "OPENAI_API_KEY"].filter((varName) => !process.env[varName]);
  if (missing.length > 0)
    throw new Error(`\u7F3A\u5C11\u5FC5\u9700\u7684\u73AF\u5883\u53D8\u91CF: ${missing.join(", ")}\u3002\u8BF7\u68C0\u67E5 .env \u6587\u4EF6\u662F\u5426\u6B63\u786E\u914D\u7F6E\u3002`);
}
var exa = new Exa(process.env.EXA_API_KEY), openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});
async function searchPropertyInfo(address) {
  try {
    console.log(`\u{1F50D} \u5F00\u59CB\u641C\u7D22\u623F\u4EA7\u4FE1\u606F: ${address}`), checkEnvironmentVariables();
    let searchResults = await searchWithExa(address);
    if (searchResults.length === 0)
      throw new Error("EXA\u641C\u7D22\u672A\u627E\u5230\u76F8\u5173\u7ED3\u679C");
    console.log("searchResults:", searchResults);
    let scrapedData = await scrapePropertyDetails(searchResults), finalData = processAndReturnData(address, searchResults, scrapedData);
    return console.log(`\u2705 \u641C\u7D22\u5B8C\u6210\uFF0C\u6210\u529F\u5904\u7406 ${finalData.successfulSources} \u4E2A\u7F51\u7AD9`), {
      success: !0,
      data: finalData
    };
  } catch (error) {
    return console.error("\u274C \u623F\u4EA7\u641C\u7D22\u5931\u8D25:", error), {
      success: !1,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
async function searchWithExa(address, options = {}) {
  console.log("\u{1F4E1} \u6267\u884CEXA\u641C\u7D22...");
  let {
    numResults = 5,
    useAutoprompt = !1,
    type = "keyword",
    includeDomains = [
      "domain.com.au",
      "realestate.com.au",
      "realtor.com",
      "view.com.au",
      "onthehouse.com.au",
      "propertyvalue.com.au",
      "reiwa.com.au",
      "allhomes.com.au",
      "findbesthouse.com"
    ],
    excludeDomains = [],
    startCrawlDate = null,
    endCrawlDate = null,
    includeText = [],
    excludeText = [],
    context = !1
  } = options, searchQuery = address, searchParams = {
    query: searchQuery,
    numResults,
    useAutoprompt,
    type,
    includeDomains,
    excludeDomains,
    startCrawlDate,
    endCrawlDate,
    includeText,
    excludeText,
    context
  };
  return console.log("searchQuery:", searchQuery), console.log("searchParams:", searchParams), (await exa.search(searchQuery, searchParams)).results.map((result) => ({
    title: result.title,
    url: result.url,
    text: result.text,
    score: result.score
  }));
}
async function scrapePropertyDetails(searchResults) {
  console.log("\u{1F577}\uFE0F \u5F00\u59CB\u6293\u53D6\u623F\u4EA7\u8BE6\u60C5...");
  let filteredResults = filterBySite(searchResults);
  console.log(`\u{1F4CB} \u53BB\u91CD\u540E\u4FDD\u7559 ${filteredResults.length} \u4E2A\u7F51\u7AD9`);
  let browser = await puppeteer.launch({
    headless: !0,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-web-security"
    ]
  });
  try {
    let results = [];
    for (let i = 0; i < filteredResults.length; i += 5) {
      let batchPromises = filteredResults.slice(i, i + 5).map(async (result) => {
        try {
          return await scrapePropertyPage(browser, result);
        } catch (error) {
          return console.log(`\u274C \u6293\u53D6\u5931\u8D25: ${result.title} - ${error}`), null;
        }
      }), batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter((r) => r !== null)), i + 5 < filteredResults.length && await new Promise((resolve) => setTimeout(resolve, 2e3));
    }
    return results;
  } finally {
    await browser.close();
  }
}
function filterBySite(searchResults) {
  let siteSeen = /* @__PURE__ */ new Set(), filtered = [];
  for (let result of searchResults) {
    let siteName = getSiteName(result.url);
    siteSeen.has(siteName) || (siteSeen.add(siteName), filtered.push(result));
  }
  return filtered;
}
function getSiteName(url) {
  return url.includes("domain.com.au") ? "Domain" : url.includes("realestate.com.au") ? "RealEstate.com.au" : url.includes("realtor.com") ? "Realtor.com" : url.includes("view.com.au") ? "View.com.au" : url.includes("onthehouse.com.au") ? "OnTheHouse.com.au" : url.includes("propertyvalue.com.au") ? "PropertyValue.com.au" : "Unknown";
}
async function scrapePropertyPage(browser, searchResult) {
  let page = await browser.newPage();
  try {
    await page.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"), await page.setExtraHTTPHeaders({
      Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      DNT: "1"
    }), await page.goto(searchResult.url, {
      waitUntil: "domcontentloaded",
      timeout: 3e4
    }), await new Promise((resolve) => setTimeout(resolve, 3e3));
    let htmlContent = await page.content();
    return await extractDataWithLLM(htmlContent, searchResult.url);
  } catch (error) {
    return console.log(`\u6293\u53D6\u9875\u9762\u5931\u8D25: ${searchResult.url} - ${error}`), null;
  } finally {
    await page.close();
  }
}
async function extractDataWithLLM(htmlContent, url) {
  let siteName = getSiteName(url), prompt = `\u4F60\u662F\u4E00\u4E2A\u623F\u4EA7\u4FE1\u606F\u63D0\u53D6\u4E13\u5BB6\u3002\u8BF7\u5206\u6790\u4EE5\u4E0BHTML\u5185\u5BB9\uFF0C\u63D0\u53D6\u623F\u4EA7\u4FE1\u606F\u3002

**\u91CD\u8981**: \u4F60\u5FC5\u987B\u53EA\u8FD4\u56DEJSON\u683C\u5F0F\u7684\u6570\u636E\uFF0C\u4E0D\u8981\u6DFB\u52A0\u4EFB\u4F55\u5176\u4ED6\u6587\u5B57\u8BF4\u660E\u3002

\u4ECEHTML\u4E2D\u63D0\u53D6\u4EE5\u4E0B\u4FE1\u606F\uFF0C\u5982\u679C\u627E\u4E0D\u5230\u5C31\u5199"N/A"\u3002

\u8FD4\u56DE\u683C\u5F0F\u793A\u4F8B\uFF1A
{
  "siteName": "${siteName}",
  "address": "\u5B8C\u6574\u5730\u5740",
  "propertyType": "\u623F\u4EA7\u7C7B\u578B",
  "bedrooms": "\u5367\u5BA4\u6570\u91CF",
  "bathrooms": "\u6D74\u5BA4\u6570\u91CF",
  "parking": "\u505C\u8F66\u4F4D\u6570\u91CF",
  "landSize": "\u571F\u5730\u9762\u79EF",
  "buildingSize": "\u5EFA\u7B51\u9762\u79EF",
  "yearBuilt": "\u5EFA\u6210\u5E74\u4EFD",
  "currentGuidePrice": "\u5F53\u524Dguide\u4EF7\u683C",
  "estimatedValueRange": "\u4F30\u4EF7\u8303\u56F4",
  "estimatedValueMid": "\u4F30\u4EF7\u4E2D\u4F4D\u6570",
  "auctionDate": "\u62CD\u5356\u65E5\u671F\u548C\u65F6\u95F4",
  "inspectionTimes": ["\u5F00\u653E\u68C0\u67E5\u65F6\u95F41", "\u5F00\u653E\u68C0\u67E5\u65F6\u95F42"],
  "historyRecords": [
    {
      "type": "listing|sale",
      "date": "\u65E5\u671F",
      "price": "\u4EF7\u683C\u6216guide\u4EF7\u683C",
      "details": "\u8BE6\u7EC6\u4FE1\u606F"
    }
  ],
  "description": "\u623F\u4EA7\u7B80\u77ED\u63CF\u8FF0",
  "features": ["\u7279\u82721", "\u7279\u82722"],
  "contact": "\u8054\u7CFB\u65B9\u5F0F"
}

HTML\u5185\u5BB9\uFF1A
${htmlContent.substring(0, 3e4)}`;
  try {
    let content = (await openai.chat.completions.create({
      model: process.env.MODEL_NAME || "gpt-5-nano",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ]
    })).choices[0].message.content, propertyData, codeBlockMatch = content?.match(/```json\s*([\s\S]*?)\s*```/), jsonText = null;
    if (codeBlockMatch)
      jsonText = codeBlockMatch[1];
    else {
      let jsonMatch = content?.match(/\{[\s\S]*\}/);
      jsonMatch && (jsonText = jsonMatch[0]);
    }
    if (jsonText)
      try {
        return propertyData = JSON.parse(jsonText), propertyData.sourceUrl = url, propertyData;
      } catch {
        console.log("JSON\u89E3\u6790\u5931\u8D25\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
      }
    return createDefaultPropertyInfo(siteName, url);
  } catch (error) {
    return console.log(`LLM\u5904\u7406\u9519\u8BEF: ${error}`), createDefaultPropertyInfo(siteName, url);
  }
}
function createDefaultPropertyInfo(siteName, url) {
  return {
    siteName,
    address: "N/A",
    propertyType: "N/A",
    bedrooms: "N/A",
    bathrooms: "N/A",
    parking: "N/A",
    landSize: "N/A",
    buildingSize: "N/A",
    yearBuilt: "N/A",
    currentGuidePrice: "N/A",
    estimatedValueRange: "N/A",
    estimatedValueMid: "N/A",
    auctionDate: "N/A",
    inspectionTimes: [],
    historyRecords: [],
    description: "N/A",
    features: [],
    contact: "N/A",
    sourceUrl: url
  };
}
function processAndReturnData(address, searchResults, scrapedData) {
  return {
    id: `property-${Date.now()}`,
    address,
    searchDate: (/* @__PURE__ */ new Date()).toISOString(),
    totalSources: searchResults.length,
    successfulSources: scrapedData.length,
    properties: scrapedData
  };
}

// app/routes/api.search-property.ts
async function action({ request }) {
  if (request.method !== "POST")
    return json({ error: "Method not allowed" }, { status: 405 });
  try {
    let { address } = await request.json();
    if (!address || typeof address != "string")
      return json({ error: "Invalid address provided" }, { status: 400 });
    console.log(`\u{1F50D} API: \u5F00\u59CB\u5904\u7406\u623F\u4EA7\u5730\u5740: ${address}`);
    let result = await searchPropertyInfo(address);
    return result.success ? json(result) : json(result, { status: 500 });
  } catch (error) {
    return console.error("\u274C API: \u5DE5\u4F5C\u6D41\u7A0B\u5931\u8D25:", error), json({
      success: !1,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// app/routes/_index.tsx
var index_exports = {};
__export(index_exports, {
  default: () => Index
});
import { useState, useEffect } from "react";

// app/utils/property-client.ts
var localStorageUtils = {
  saveProperties: (properties) => {
    typeof window < "u" && localStorage.setItem("propertySearchResults", JSON.stringify(properties));
  },
  loadProperties: () => {
    if (typeof window < "u") {
      let saved = localStorage.getItem("propertySearchResults");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  },
  removeProperty: (index) => {
    if (typeof window < "u") {
      let properties = localStorageUtils.loadProperties();
      return properties.splice(index, 1), localStorageUtils.saveProperties(properties), properties;
    }
    return [];
  }
}, clipboardUtils = {
  readText: async () => {
    if (typeof window < "u" && navigator.clipboard)
      try {
        return await navigator.clipboard.readText();
      } catch (error) {
        throw console.error("\u65E0\u6CD5\u8BFB\u53D6\u526A\u8D34\u677F:", error), new Error("\u65E0\u6CD5\u8BBF\u95EE\u526A\u8D34\u677F\uFF0C\u8BF7\u68C0\u67E5\u6D4F\u89C8\u5668\u6743\u9650");
      }
    throw new Error("\u526A\u8D34\u677FAPI\u4E0D\u53EF\u7528");
  }
};

// app/components/SearchButton.tsx
import { jsxDEV as jsxDEV3 } from "react/jsx-dev-runtime";
function SearchButton({ isLoading, onClick }) {
  return /* @__PURE__ */ jsxDEV3(
    "button",
    {
      style: {
        background: isLoading ? "linear-gradient(135deg, #94a3b8 0%, #64748b 100%)" : "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
        color: "white",
        border: "none",
        padding: "16px 32px",
        fontSize: "18px",
        fontWeight: "600",
        borderRadius: "12px",
        cursor: isLoading ? "not-allowed" : "pointer",
        boxShadow: isLoading ? "0 4px 15px rgba(0,0,0,0.1)" : "0 6px 20px rgba(59, 130, 246, 0.3)",
        minWidth: "280px",
        transition: "all 0.3s ease",
        transform: isLoading ? "scale(0.98)" : "scale(1)"
      },
      onClick,
      disabled: isLoading,
      children: [
        isLoading ? /* @__PURE__ */ jsxDEV3("span", { style: { display: "flex", alignItems: "center", justifyContent: "center", gap: "8px" }, children: [
          /* @__PURE__ */ jsxDEV3("span", { style: {
            width: "20px",
            height: "20px",
            border: "2px solid #ffffff40",
            borderTop: "2px solid #ffffff",
            borderRadius: "50%",
            animation: "spin 1s linear infinite"
          } }, void 0, !1, {
            fileName: "app/components/SearchButton.tsx",
            lineNumber: 34,
            columnNumber: 11
          }, this),
          "\u641C\u7D22\u4E2D..."
        ] }, void 0, !0, {
          fileName: "app/components/SearchButton.tsx",
          lineNumber: 33,
          columnNumber: 9
        }, this) : /* @__PURE__ */ jsxDEV3("span", { style: { display: "flex", alignItems: "center", justifyContent: "center", gap: "8px" }, children: "\u{1F4CB} \u4ECE\u526A\u8D34\u677F\u641C\u7D22" }, void 0, !1, {
          fileName: "app/components/SearchButton.tsx",
          lineNumber: 45,
          columnNumber: 9
        }, this),
        /* @__PURE__ */ jsxDEV3("style", { children: `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      ` }, void 0, !1, {
          fileName: "app/components/SearchButton.tsx",
          lineNumber: 49,
          columnNumber: 7
        }, this)
      ]
    },
    void 0,
    !0,
    {
      fileName: "app/components/SearchButton.tsx",
      lineNumber: 27,
      columnNumber: 5
    },
    this
  );
}

// app/components/ErrorMessage.tsx
import { jsxDEV as jsxDEV4 } from "react/jsx-dev-runtime";
function ErrorMessage({ error }) {
  return /* @__PURE__ */ jsxDEV4("div", { style: {
    background: "linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)",
    color: "#dc2626",
    padding: "16px",
    borderRadius: "12px",
    marginTop: "16px",
    border: "1px solid #fecaca",
    boxShadow: "0 4px 12px rgba(220, 38, 38, 0.1)",
    fontSize: "14px",
    lineHeight: "1.5",
    display: "flex",
    alignItems: "center",
    gap: "8px"
  }, children: [
    /* @__PURE__ */ jsxDEV4("span", { style: { fontSize: "18px" }, children: "\u26A0\uFE0F" }, void 0, !1, {
      fileName: "app/components/ErrorMessage.tsx",
      lineNumber: 23,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV4("span", { children: error }, void 0, !1, {
      fileName: "app/components/ErrorMessage.tsx",
      lineNumber: 24,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/components/ErrorMessage.tsx",
    lineNumber: 22,
    columnNumber: 5
  }, this);
}

// app/components/PropertyCard.tsx
import { jsxDEV as jsxDEV5 } from "react/jsx-dev-runtime";
function PropertyCard({ property, onDelete }) {
  let cardStyle = {
    background: "white",
    borderRadius: "16px",
    boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
    marginBottom: "24px",
    overflow: "hidden",
    border: "1px solid #f1f5f9"
  }, headerStyle = {
    background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
    padding: "20px",
    borderBottom: "1px solid #e2e8f0"
  }, deleteButtonStyle = {
    background: "#ef4444",
    color: "white",
    border: "none",
    padding: "8px 12px",
    borderRadius: "8px",
    cursor: "pointer",
    fontSize: "12px",
    fontWeight: "500",
    transition: "all 0.2s ease",
    display: "flex",
    alignItems: "center",
    gap: "4px"
  }, tableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: "14px"
  }, thStyle = {
    padding: "12px 8px",
    textAlign: "left",
    borderBottom: "2px solid #e2e8f0",
    background: "#f8fafc",
    fontWeight: "600",
    color: "#475569",
    fontSize: "13px"
  }, tdStyle = {
    padding: "12px 8px",
    textAlign: "left",
    borderBottom: "1px solid #f1f5f9",
    maxWidth: "120px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    fontSize: "13px",
    color: "#334155"
  };
  return /* @__PURE__ */ jsxDEV5("div", { style: cardStyle, children: [
    /* @__PURE__ */ jsxDEV5("div", { style: headerStyle, children: [
      /* @__PURE__ */ jsxDEV5("h3", { style: {
        margin: "0 0 12px 0",
        color: "#1e293b",
        fontSize: "18px",
        fontWeight: "700",
        lineHeight: "1.3"
      }, children: [
        "\u{1F4CD} ",
        property.address
      ] }, void 0, !0, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 69,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV5("div", { style: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        fontSize: "13px",
        color: "#64748b",
        flexWrap: "wrap",
        gap: "8px"
      }, children: [
        /* @__PURE__ */ jsxDEV5("div", { style: { display: "flex", flexWrap: "wrap", gap: "16px" }, children: [
          /* @__PURE__ */ jsxDEV5("span", { children: [
            "\u{1F5D3}\uFE0F ",
            new Date(property.searchDate).toLocaleString("zh-CN")
          ] }, void 0, !0, {
            fileName: "app/components/PropertyCard.tsx",
            lineNumber: 88,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV5("span", { children: [
            "\u{1F4CA} ",
            property.successfulSources,
            "/",
            property.totalSources,
            " \u6570\u636E\u6E90"
          ] }, void 0, !0, {
            fileName: "app/components/PropertyCard.tsx",
            lineNumber: 89,
            columnNumber: 13
          }, this)
        ] }, void 0, !0, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 87,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV5(
          "button",
          {
            style: deleteButtonStyle,
            onClick: onDelete,
            title: "\u5220\u9664\u8FD9\u6761\u8BB0\u5F55",
            onMouseEnter: (e) => {
              e.currentTarget.style.background = "#dc2626", e.currentTarget.style.transform = "scale(1.05)";
            },
            onMouseLeave: (e) => {
              e.currentTarget.style.background = "#ef4444", e.currentTarget.style.transform = "scale(1)";
            },
            children: "\u{1F5D1}\uFE0F \u5220\u9664"
          },
          void 0,
          !1,
          {
            fileName: "app/components/PropertyCard.tsx",
            lineNumber: 91,
            columnNumber: 11
          },
          this
        )
      ] }, void 0, !0, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 78,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/components/PropertyCard.tsx",
      lineNumber: 68,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV5("div", { style: { overflowX: "auto", padding: "0" }, children: /* @__PURE__ */ jsxDEV5("table", { style: tableStyle, children: [
      /* @__PURE__ */ jsxDEV5("thead", { children: /* @__PURE__ */ jsxDEV5("tr", { children: [
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u7F51\u7AD9" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 113,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u623F\u578B" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 114,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u5367\u5BA4" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 115,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u6D74\u5BA4" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 116,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u505C\u8F66" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 117,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u5EFA\u6210\u5E74\u4EFD" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 118,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u6307\u5BFC\u4EF7" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 119,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u4F30\u4EF7\u8303\u56F4" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 120,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u62CD\u5356\u65E5\u671F" }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 121,
          columnNumber: 15
        }, this)
      ] }, void 0, !0, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 112,
        columnNumber: 13
      }, this) }, void 0, !1, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 111,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV5("tbody", { children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV5("tr", { style: {
        transition: "background-color 0.2s ease"
      }, children: [
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: /* @__PURE__ */ jsxDEV5(
          "a",
          {
            href: prop.sourceUrl,
            target: "_blank",
            rel: "noopener noreferrer",
            style: {
              color: "#3b82f6",
              textDecoration: "none",
              fontWeight: "500",
              borderBottom: "1px solid transparent",
              transition: "border-color 0.2s ease"
            },
            onMouseEnter: (e) => {
              e.currentTarget.style.borderBottomColor = "#3b82f6";
            },
            onMouseLeave: (e) => {
              e.currentTarget.style.borderBottomColor = "transparent";
            },
            children: prop.siteName
          },
          void 0,
          !1,
          {
            fileName: "app/components/PropertyCard.tsx",
            lineNumber: 130,
            columnNumber: 19
          },
          this
        ) }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 129,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.propertyType }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 151,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.bedrooms }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 152,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.bathrooms }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 153,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.parking }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 154,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.yearBuilt }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 155,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.currentGuidePrice }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 156,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.estimatedValueRange }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 157,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.auctionDate }, void 0, !1, {
          fileName: "app/components/PropertyCard.tsx",
          lineNumber: 158,
          columnNumber: 17
        }, this)
      ] }, propIndex, !0, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 126,
        columnNumber: 15
      }, this)) }, void 0, !1, {
        fileName: "app/components/PropertyCard.tsx",
        lineNumber: 124,
        columnNumber: 11
      }, this)
    ] }, void 0, !0, {
      fileName: "app/components/PropertyCard.tsx",
      lineNumber: 110,
      columnNumber: 9
    }, this) }, void 0, !1, {
      fileName: "app/components/PropertyCard.tsx",
      lineNumber: 109,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/components/PropertyCard.tsx",
    lineNumber: 67,
    columnNumber: 5
  }, this);
}

// app/routes/_index.tsx
import { jsxDEV as jsxDEV6 } from "react/jsx-dev-runtime";
function Index() {
  let [properties, setProperties] = useState([]), [isLoading, setIsLoading] = useState(!1), [error, setError] = useState(null);
  useEffect(() => {
    let savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []), useEffect(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);
  let handleSearchClick = async () => {
    setIsLoading(!0), setError(null);
    try {
      let clipboardText = await clipboardUtils.readText();
      if (!clipboardText.trim())
        throw new Error("\u526A\u8D34\u677F\u5185\u5BB9\u4E3A\u7A7A\uFF0C\u8BF7\u5148\u590D\u5236\u623F\u4EA7\u5730\u5740");
      console.log("\u526A\u8D34\u677F\u5185\u5BB9:", clipboardText);
      let response = await fetch("/api/search-property", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ address: clipboardText.trim() })
      });
      if (!response.ok) {
        let errorData = await response.json();
        throw new Error(errorData.error || `API\u8C03\u7528\u5931\u8D25: ${response.status}`);
      }
      let result = await response.json();
      if (result.success && result.data)
        setProperties((prev) => [result.data, ...prev]);
      else
        throw new Error(result.error || "\u641C\u7D22\u5931\u8D25");
    } catch (error2) {
      console.error("\u641C\u7D22\u5931\u8D25:", error2), setError(error2 instanceof Error ? error2.message : String(error2));
    } finally {
      setIsLoading(!1);
    }
  }, handleDeleteProperty = (index) => {
    setProperties((prev) => prev.filter((_, i) => i !== index));
  };
  return /* @__PURE__ */ jsxDEV6("div", { style: {
    maxWidth: "1200px",
    margin: "0 auto",
    padding: "20px",
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    minHeight: "100vh",
    background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)"
  }, children: [
    /* @__PURE__ */ jsxDEV6("div", { style: { textAlign: "center", marginBottom: "40px" }, children: [
      /* @__PURE__ */ jsxDEV6("h1", { style: {
        color: "#1e293b",
        marginBottom: "12px",
        fontSize: "28px",
        fontWeight: "800",
        background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text"
      }, children: "\u{1F3E0} \u623F\u4EA7\u4FE1\u606F\u641C\u7D22" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 85,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV6("p", { style: {
        color: "#64748b",
        fontSize: "16px",
        lineHeight: "1.5",
        maxWidth: "400px",
        margin: "0 auto"
      }, children: "\u590D\u5236\u623F\u4EA7\u5730\u5740\u5230\u526A\u8D34\u677F\uFF0C\u7136\u540E\u70B9\u51FB\u6309\u94AE\u5F00\u59CB\u641C\u7D22" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 97,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 84,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV6("div", { style: { textAlign: "center", marginBottom: "40px" }, children: [
      /* @__PURE__ */ jsxDEV6(
        SearchButton,
        {
          isLoading,
          onClick: handleSearchClick
        },
        void 0,
        !1,
        {
          fileName: "app/routes/_index.tsx",
          lineNumber: 110,
          columnNumber: 9
        },
        this
      ),
      error && /* @__PURE__ */ jsxDEV6(ErrorMessage, { error }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 115,
        columnNumber: 19
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 109,
      columnNumber: 7
    }, this),
    properties.length > 0 && /* @__PURE__ */ jsxDEV6("div", { children: [
      /* @__PURE__ */ jsxDEV6("div", { style: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "24px",
        padding: "0 4px"
      }, children: [
        /* @__PURE__ */ jsxDEV6("h2", { style: {
          color: "#1e293b",
          margin: "0",
          fontSize: "20px",
          fontWeight: "700"
        }, children: "\u641C\u7D22\u7ED3\u679C" }, void 0, !1, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 128,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV6("span", { style: {
          background: "#3b82f6",
          color: "white",
          padding: "4px 12px",
          borderRadius: "20px",
          fontSize: "14px",
          fontWeight: "600"
        }, children: [
          properties.length,
          " \u6761\u8BB0\u5F55"
        ] }, void 0, !0, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 136,
          columnNumber: 13
        }, this)
      ] }, void 0, !0, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 121,
        columnNumber: 11
      }, this),
      properties.map((property, index) => /* @__PURE__ */ jsxDEV6(
        PropertyCard,
        {
          property,
          onDelete: () => handleDeleteProperty(index)
        },
        property.id,
        !1,
        {
          fileName: "app/routes/_index.tsx",
          lineNumber: 149,
          columnNumber: 13
        },
        this
      ))
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 120,
      columnNumber: 9
    }, this),
    properties.length === 0 && !isLoading && /* @__PURE__ */ jsxDEV6("div", { style: {
      textAlign: "center",
      padding: "60px 20px",
      color: "#64748b"
    }, children: [
      /* @__PURE__ */ jsxDEV6("div", { style: { fontSize: "48px", marginBottom: "16px" }, children: "\u{1F3E1}" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 165,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV6("h3", { style: {
        margin: "0 0 8px 0",
        color: "#475569",
        fontSize: "18px",
        fontWeight: "600"
      }, children: "\u8FD8\u6CA1\u6709\u641C\u7D22\u8BB0\u5F55" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 166,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV6("p", { style: { margin: "0", fontSize: "14px" }, children: "\u590D\u5236\u623F\u4EA7\u5730\u5740\u5E76\u70B9\u51FB\u641C\u7D22\u6309\u94AE\u5F00\u59CB\u4F7F\u7528" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 174,
        columnNumber: 11
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 160,
      columnNumber: 9
    }, this)
  ] }, void 0, !0, {
    fileName: "app/routes/_index.tsx",
    lineNumber: 82,
    columnNumber: 5
  }, this);
}

// server-assets-manifest:@remix-run/dev/assets-manifest
var assets_manifest_default = { entry: { module: "/build/entry.client-HJQELB22.js", imports: ["/build/_shared/chunk-O4BRYNJ4.js", "/build/_shared/chunk-V3BJQ67B.js", "/build/_shared/chunk-U4FRFQSK.js", "/build/_shared/chunk-XGOTYLZ5.js", "/build/_shared/chunk-X3T7OMQU.js", "/build/_shared/chunk-UWV35TSL.js", "/build/_shared/chunk-7M6SC7J5.js", "/build/_shared/chunk-PNG5AS42.js"] }, routes: { root: { id: "root", parentId: void 0, path: "", index: void 0, caseSensitive: void 0, module: "/build/root-7CUIXVDC.js", imports: void 0, hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/_index": { id: "routes/_index", parentId: "root", path: void 0, index: !0, caseSensitive: void 0, module: "/build/routes/_index-AOPVVPKB.js", imports: void 0, hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/api.search-property": { id: "routes/api.search-property", parentId: "root", path: "api/search-property", index: void 0, caseSensitive: void 0, module: "/build/routes/api.search-property-JHHPWYLI.js", imports: void 0, hasAction: !0, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 } }, version: "2b82642f", hmr: { runtime: "/build/_shared\\chunk-X3T7OMQU.js", timestamp: 1758972219135 }, url: "/build/manifest-2B82642F.js" };

// server-entry-module:@remix-run/dev/server-build
var mode = "development", assetsBuildDirectory = "public\\build", future = { v3_fetcherPersist: !1, v3_relativeSplatPath: !1, v3_throwAbortReason: !1, v3_routeConfig: !1, v3_singleFetch: !1, v3_lazyRouteDiscovery: !1, unstable_optimizeDeps: !1 }, publicPath = "/build/", entry = { module: entry_server_exports }, routes = {
  root: {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: root_exports
  },
  "routes/api.search-property": {
    id: "routes/api.search-property",
    parentId: "root",
    path: "api/search-property",
    index: void 0,
    caseSensitive: void 0,
    module: api_search_property_exports
  },
  "routes/_index": {
    id: "routes/_index",
    parentId: "root",
    path: void 0,
    index: !0,
    caseSensitive: void 0,
    module: index_exports
  }
};
export {
  assets_manifest_default as assets,
  assetsBuildDirectory,
  entry,
  future,
  mode,
  publicPath,
  routes
};
//# sourceMappingURL=index.js.map
