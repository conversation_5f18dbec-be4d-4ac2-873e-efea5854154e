{"inputs": {"app/entry.server.tsx": {"bytes": 597, "imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/root.tsx": {"bytes": 2834, "imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/utils/property-search.server.ts": {"bytes": 11823, "imports": [{"path": "exa-js", "kind": "import-statement", "external": true}, {"path": "puppeteer-extra", "kind": "import-statement", "external": true}, {"path": "puppeteer-extra-plugin-stealth", "kind": "import-statement", "external": true}, {"path": "openai", "kind": "import-statement", "external": true}], "format": "esm"}, "app/routes/api.search-property.ts": {"bytes": 1068, "imports": [{"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "app/utils/property-search.server.ts", "kind": "import-statement", "original": "~/utils/property-search.server"}], "format": "esm"}, "app/utils/property-client.ts": {"bytes": 2076, "imports": [], "format": "esm"}, "app/components/SearchButton.tsx": {"bytes": 1642, "imports": [{"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/components/ErrorMessage.tsx": {"bytes": 642, "imports": [{"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/components/PropertyCard.tsx": {"bytes": 5134, "imports": [{"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/routes/_index.tsx": {"bytes": 5464, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "app/utils/property-client.ts", "kind": "import-statement", "original": "~/utils/property-client"}, {"path": "app/components/SearchButton.tsx", "kind": "import-statement", "original": "~/components/SearchButton"}, {"path": "app/components/ErrorMessage.tsx", "kind": "import-statement", "original": "~/components/ErrorMessage"}, {"path": "app/components/PropertyCard.tsx", "kind": "import-statement", "original": "~/components/PropertyCard"}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "server-assets-manifest:@remix-run/dev/assets-manifest": {"bytes": 1397, "imports": [], "format": "esm"}, "server-entry-module:@remix-run/dev/server-build": {"bytes": 1288, "imports": [{"path": "app/entry.server.tsx", "kind": "import-statement", "original": "C:\\Users\\<USER>\\projects\\property_helper\\app\\entry.server.tsx"}, {"path": "app/root.tsx", "kind": "import-statement", "original": "./root.tsx"}, {"path": "app/routes/api.search-property.ts", "kind": "import-statement", "original": "./routes/api.search-property.ts"}, {"path": "app/routes/_index.tsx", "kind": "import-statement", "original": "./routes/_index.tsx"}, {"path": "server-assets-manifest:@remix-run/dev/assets-manifest", "kind": "import-statement", "original": "@remix-run/dev/assets-manifest"}], "format": "esm"}, "<stdin>": {"bytes": 44, "imports": [{"path": "server-entry-module:@remix-run/dev/server-build", "kind": "import-statement", "original": "@remix-run/dev/server-build"}], "format": "esm"}}, "outputs": {"build/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 56563}, "build/index.js": {"imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "exa-js", "kind": "import-statement", "external": true}, {"path": "puppeteer-extra", "kind": "import-statement", "external": true}, {"path": "puppeteer-extra-plugin-stealth", "kind": "import-statement", "external": true}, {"path": "openai", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "exports": ["assets", "assetsBuildDirectory", "entry", "future", "mode", "publicPath", "routes"], "entryPoint": "<stdin>", "inputs": {"app/entry.server.tsx": {"bytesInOutput": 734}, "app/root.tsx": {"bytesInOutput": 4532}, "app/routes/api.search-property.ts": {"bytesInOutput": 910}, "app/utils/property-search.server.ts": {"bytesInOutput": 8906}, "app/routes/_index.tsx": {"bytesInOutput": 7110}, "app/utils/property-client.ts": {"bytesInOutput": 1070}, "app/components/SearchButton.tsx": {"bytesInOutput": 2413}, "app/components/ErrorMessage.tsx": {"bytesInOutput": 1036}, "app/components/PropertyCard.tsx": {"bytesInOutput": 10450}, "server-assets-manifest:@remix-run/dev/assets-manifest": {"bytesInOutput": 1355}, "server-entry-module:@remix-run/dev/server-build": {"bytesInOutput": 841}, "<stdin>": {"bytesInOutput": 0}}, "bytes": 40147}}}