import { Exa } from 'exa-js';
import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import OpenAI from 'openai';

// 添加stealth插件
puppeteer.use(StealthPlugin());

// 类型定义
export interface PropertySearchResult {
  success: boolean;
  data?: PropertyData;
  error?: string;
}

export interface PropertyData {
  id: string;
  address: string;
  searchDate: string;
  totalSources: number;
  successfulSources: number;
  properties: PropertyInfo[];
}

export interface PropertyInfo {
  siteName: string;
  address: string;
  propertyType: string;
  bedrooms: string;
  bathrooms: string;
  parking: string;
  landSize: string;
  buildingSize: string;
  yearBuilt: string;
  currentGuidePrice: string;
  estimatedValueRange: string;
  estimatedValueMid: string;
  auctionDate: string;
  inspectionTimes: string[];
  historyRecords: any[];
  description: string;
  features: string[];
  contact: string;
  sourceUrl: string;
}

interface SearchResult {
  title: string;
  url: string;
  text?: string;
  score?: number;
}

// 环境变量检查
function checkEnvironmentVariables() {
  const requiredVars = ['EXA_API_KEY', 'OPENAI_API_KEY'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}。请检查 .env 文件是否正确配置。`);
  }
}

// 初始化客户端
const exa = new Exa(process.env.EXA_API_KEY!);
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!
});

/**
 * 主要的房产搜索函数 - 集成3个核心功能
 * 1. 通过EXA搜索，返回5条结果
 * 2. 站点去重+并发访问+LLM解析
 * 3. 汇总并返回数据
 */
export async function searchPropertyInfo(address: string): Promise<PropertySearchResult> {
  try {
    console.log(`🔍 开始搜索房产信息: ${address}`);
    
    // 检查环境变量
    checkEnvironmentVariables();
    
    // 功能1: EXA搜索
    const searchResults = await searchWithExa(address);
    if (searchResults.length === 0) {
      throw new Error('EXA搜索未找到相关结果');
    }

    console.log('searchResults:', searchResults);
    
    // 功能2: 站点去重+并发访问+LLM解析
    const scrapedData = await scrapePropertyDetails(searchResults);
    
    // 功能3: 汇总并返回数据
    const finalData = processAndReturnData(address, searchResults, scrapedData);
    
    console.log(`✅ 搜索完成，成功处理 ${finalData.successfulSources} 个网站`);
    
    return {
      success: true,
      data: finalData
    };
    
  } catch (error) {
    console.error('❌ 房产搜索失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 功能1: 通过EXA搜索，返回5条结果
 */
async function searchWithExa(address: string, options = {}): Promise<SearchResult[]> {
  console.log('📡 执行EXA搜索...');

  // 默认选项
  const {
    numResults = 5,
    useAutoprompt = false,
    type = 'keyword',
    includeDomains = [
        'domain.com.au',
        'realestate.com.au', 
        'realtor.com',
        'view.com.au',
        'onthehouse.com.au',
        'propertyvalue.com.au',
        'reiwa.com.au',
        'allhomes.com.au',
        'findbesthouse.com'
    ],
    excludeDomains = [],
    startCrawlDate = null,
    endCrawlDate = null,
    includeText = [],
    excludeText = [],
    context = false
  } = options;
  
  const searchQuery = address;
  
  // // 使用正确的EXA API调用方式
  // const searchResults = await exa.searchAndContents(searchQuery, {
  //   numResults: 5,
  //   useAutoprompt: true,
  //   type: 'neural',
  //   includeDomains: [
  //     'domain.com.au',
  //     'realestate.com.au', 
  //     'realtor.com',
  //     'view.com.au',
  //     'onthehouse.com.au',
  //     'propertyvalue.com.au',
  //     'reiwa.com.au',
  //     'allhomes.com.au'
  //   ]
  // });
  
  // console.log('searchResults.results:', searchResults.results);
  // console.log(`✅ EXA搜索完成，找到 ${searchResults.results.length} 个结果`);

  // 构建搜索参数
  const searchParams = {
      query: searchQuery,
      numResults: numResults,
      useAutoprompt: useAutoprompt,
      type: type,
      includeDomains,
      excludeDomains,
      startCrawlDate,
      endCrawlDate,
      includeText,
      excludeText,
      context
  };

  console.log('searchQuery:', searchQuery);
  console.log('searchParams:', searchParams);
  const searchResults = await exa.search(searchQuery, searchParams);
  
  return searchResults.results.map((result: any) => ({
    title: result.title,
    url: result.url,
    text: result.text,
    score: result.score
  }));
}

/**
 * 功能2: 站点去重+并发访问+LLM解析
 */
async function scrapePropertyDetails(searchResults: SearchResult[]): Promise<PropertyInfo[]> {
  console.log('🕷️ 开始抓取房产详情...');
  
  // 站点去重
  const filteredResults = filterBySite(searchResults);
  console.log(`📋 去重后保留 ${filteredResults.length} 个网站`);
  
  // 启动浏览器
  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security'
    ]
  });
  
  try {
    // 并发处理（限制并发数为3）
    const CONCURRENT_LIMIT = 5;
    const results: PropertyInfo[] = [];
    
    for (let i = 0; i < filteredResults.length; i += CONCURRENT_LIMIT) {
      const batch = filteredResults.slice(i, i + CONCURRENT_LIMIT);
      
      const batchPromises = batch.map(async (result) => {
        try {
          return await scrapePropertyPage(browser, result);
        } catch (error) {
          console.log(`❌ 抓取失败: ${result.title} - ${error}`);
          return null;
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(r => r !== null) as PropertyInfo[]);
      
      // 批次间延迟
      if (i + CONCURRENT_LIMIT < filteredResults.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    return results;
    
  } finally {
    await browser.close();
  }
}

/**
 * 站点去重 - 每个网站只保留第一个链接
 */
function filterBySite(searchResults: SearchResult[]): SearchResult[] {
  const siteSeen = new Set<string>();
  const filtered: SearchResult[] = [];
  
  for (const result of searchResults) {
    const siteName = getSiteName(result.url);
    if (!siteSeen.has(siteName)) {
      siteSeen.add(siteName);
      filtered.push(result);
    }
  }
  
  return filtered;
}

/**
 * 获取网站名称
 */
function getSiteName(url: string): string {
  if (url.includes('domain.com.au')) return 'Domain';
  if (url.includes('realestate.com.au')) return 'RealEstate.com.au';
  if (url.includes('realtor.com')) return 'Realtor.com';
  if (url.includes('view.com.au')) return 'View.com.au';
  if (url.includes('onthehouse.com.au')) return 'OnTheHouse.com.au';
  if (url.includes('propertyvalue.com.au')) return 'PropertyValue.com.au';
  return 'Unknown';
}

/**
 * 抓取单个房产页面
 */
async function scrapePropertyPage(browser: any, searchResult: SearchResult): Promise<PropertyInfo | null> {
  const page = await browser.newPage();

  try {
    // 设置隐身模式
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1'
    });

    // 访问页面
    await page.goto(searchResult.url, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 获取页面HTML内容
    const htmlContent = await page.content();

    // 使用LLM解析内容
    const propertyData = await extractDataWithLLM(htmlContent, searchResult.url);

    return propertyData;

  } catch (error) {
    console.log(`抓取页面失败: ${searchResult.url} - ${error}`);
    return null;
  } finally {
    await page.close();
  }
}

/**
 * 使用LLM解析HTML内容
 */
async function extractDataWithLLM(htmlContent: string, url: string): Promise<PropertyInfo> {
  const siteName = getSiteName(url);

  const prompt = `你是一个房产信息提取专家。请分析以下HTML内容，提取房产信息。

**重要**: 你必须只返回JSON格式的数据，不要添加任何其他文字说明。

从HTML中提取以下信息，如果找不到就写"N/A"。

返回格式示例：
{
  "siteName": "${siteName}",
  "address": "完整地址",
  "propertyType": "房产类型",
  "bedrooms": "卧室数量",
  "bathrooms": "浴室数量",
  "parking": "停车位数量",
  "landSize": "土地面积",
  "buildingSize": "建筑面积",
  "yearBuilt": "建成年份",
  "currentGuidePrice": "当前guide价格",
  "estimatedValueRange": "估价范围",
  "estimatedValueMid": "估价中位数",
  "auctionDate": "拍卖日期和时间",
  "inspectionTimes": ["开放检查时间1", "开放检查时间2"],
  "historyRecords": [
    {
      "type": "listing|sale",
      "date": "日期",
      "price": "价格或guide价格",
      "details": "详细信息"
    }
  ],
  "description": "房产简短描述",
  "features": ["特色1", "特色2"],
  "contact": "联系方式"
}

HTML内容：
${htmlContent.substring(0, 30000)}`;

  try {
    const response = await openai.chat.completions.create({
      model: process.env.MODEL_NAME || 'gpt-5-nano',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    });

    const content = response.choices[0].message.content;

    // 解析JSON响应
    let propertyData;
    const codeBlockMatch = content?.match(/```json\s*([\s\S]*?)\s*```/);
    let jsonText = null;

    if (codeBlockMatch) {
      jsonText = codeBlockMatch[1];
    } else {
      const jsonMatch = content?.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonText = jsonMatch[0];
      }
    }

    if (jsonText) {
      try {
        propertyData = JSON.parse(jsonText);
        propertyData.sourceUrl = url;
        return propertyData;
      } catch (parseError) {
        console.log('JSON解析失败，使用默认数据');
      }
    }

    // 如果解析失败，返回默认数据
    return createDefaultPropertyInfo(siteName, url);

  } catch (error) {
    console.log(`LLM处理错误: ${error}`);
    return createDefaultPropertyInfo(siteName, url);
  }
}

/**
 * 创建默认房产信息
 */
function createDefaultPropertyInfo(siteName: string, url: string): PropertyInfo {
  return {
    siteName: siteName,
    address: 'N/A',
    propertyType: 'N/A',
    bedrooms: 'N/A',
    bathrooms: 'N/A',
    parking: 'N/A',
    landSize: 'N/A',
    buildingSize: 'N/A',
    yearBuilt: 'N/A',
    currentGuidePrice: 'N/A',
    estimatedValueRange: 'N/A',
    estimatedValueMid: 'N/A',
    auctionDate: 'N/A',
    inspectionTimes: [],
    historyRecords: [],
    description: 'N/A',
    features: [],
    contact: 'N/A',
    sourceUrl: url
  };
}

/**
 * 功能3: 汇总并返回数据
 */
function processAndReturnData(address: string, searchResults: SearchResult[], scrapedData: PropertyInfo[]): PropertyData {
  return {
    id: `property-${Date.now()}`,
    address: address,
    searchDate: new Date().toISOString(),
    totalSources: searchResults.length,
    successfulSources: scrapedData.length,
    properties: scrapedData
  };
}
