{"version": 3, "sources": ["../app/entry.server.tsx", "../app/root.tsx", "../app/routes/api.search-property.ts", "../app/utils/property-search.server.ts", "../app/routes/_index.tsx", "../app/utils/property-client.ts", "../app/components/SearchButton.tsx", "../app/components/ErrorMessage.tsx", "../app/components/PropertyCard.tsx", "server-assets-manifest:@remix-run/dev/assets-manifest", "server-entry-module:@remix-run/dev/server-build"], "sourcesContent": ["import type { EntryContext } from \"@remix-run/node\";\r\nimport { RemixServer } from \"@remix-run/react\";\r\nimport { renderToString } from \"react-dom/server\";\r\n\r\nexport default function handleRequest(\r\n  request: Request,\r\n  responseStatusCode: number,\r\n  responseHeaders: Headers,\r\n  remixContext: EntryContext\r\n) {\r\n  const markup = renderToString(\r\n    <RemixServer context={remixContext} url={request.url} />\r\n  );\r\n\r\n  responseHeaders.set(\"Content-Type\", \"text/html\");\r\n\r\n  return new Response(\"<!DOCTYPE html>\" + markup, {\r\n    status: responseStatusCode,\r\n    headers: responseHeaders,\r\n  });\r\n}", "import {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>rollRestoration,\r\n} from \"@remix-run/react\";\r\n\r\nexport default function App() {\r\n  return (\r\n    <html lang=\"zh-CN\">\r\n      <head>\r\n        <meta charSet=\"utf-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <Meta />\r\n        <Links />\r\n        <style>{`\r\n          * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n          }\r\n\r\n          body {\r\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\r\n            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n            color: #1e293b;\r\n            line-height: 1.6;\r\n            -webkit-font-smoothing: antialiased;\r\n            -moz-osx-font-smoothing: grayscale;\r\n          }\r\n\r\n          /* 移动端优化 */\r\n          @media (max-width: 768px) {\r\n            body {\r\n              font-size: 14px;\r\n            }\r\n\r\n            table {\r\n              font-size: 12px;\r\n            }\r\n\r\n            th, td {\r\n              padding: 8px 4px !important;\r\n              min-width: 80px;\r\n            }\r\n\r\n            .container {\r\n              padding: 16px;\r\n            }\r\n\r\n            h1 {\r\n              font-size: 24px !important;\r\n            }\r\n\r\n            h2 {\r\n              font-size: 18px !important;\r\n            }\r\n\r\n            h3 {\r\n              font-size: 16px !important;\r\n            }\r\n          }\r\n\r\n          /* 小屏幕优化 */\r\n          @media (max-width: 480px) {\r\n            .container {\r\n              padding: 12px;\r\n            }\r\n\r\n            table {\r\n              font-size: 11px;\r\n            }\r\n\r\n            th, td {\r\n              padding: 6px 3px !important;\r\n              min-width: 70px;\r\n            }\r\n          }\r\n\r\n          /* 滚动条样式 */\r\n          ::-webkit-scrollbar {\r\n            width: 6px;\r\n            height: 6px;\r\n          }\r\n\r\n          ::-webkit-scrollbar-track {\r\n            background: #f1f5f9;\r\n            border-radius: 3px;\r\n          }\r\n\r\n          ::-webkit-scrollbar-thumb {\r\n            background: #cbd5e1;\r\n            border-radius: 3px;\r\n          }\r\n\r\n          ::-webkit-scrollbar-thumb:hover {\r\n            background: #94a3b8;\r\n          }\r\n\r\n          /* 表格响应式 */\r\n          .table-container {\r\n            overflow-x: auto;\r\n            -webkit-overflow-scrolling: touch;\r\n          }\r\n\r\n          /* 按钮触摸优化 */\r\n          button {\r\n            -webkit-tap-highlight-color: transparent;\r\n            touch-action: manipulation;\r\n          }\r\n        `}</style>\r\n      </head>\r\n      <body>\r\n        <Outlet />\r\n        <ScrollRestoration />\r\n        <Scripts />\r\n        <LiveReload />\r\n      </body>\r\n    </html>\r\n  );\r\n}", "import { json } from \"@remix-run/node\";\r\nimport type { ActionFunctionArgs } from \"@remix-run/node\";\r\nimport { searchPropertyInfo } from \"~/utils/property-search.server\";\r\n\r\nexport async function action({ request }: ActionFunctionArgs) {\r\n  if (request.method !== \"POST\") {\r\n    return json({ error: \"Method not allowed\" }, { status: 405 });\r\n  }\r\n\r\n  try {\r\n    const { address } = await request.json();\r\n    \r\n    if (!address || typeof address !== 'string') {\r\n      return json({ error: \"Invalid address provided\" }, { status: 400 });\r\n    }\r\n\r\n    console.log(`🔍 API: 开始处理房产地址: ${address}`);\r\n\r\n    // 执行完整的搜索工作流\r\n    const result = await searchPropertyInfo(address);\r\n    \r\n    if (result.success) {\r\n      return json(result);\r\n    } else {\r\n      return json(result, { status: 500 });\r\n    }\r\n    \r\n  } catch (error) {\r\n    console.error('❌ API: 工作流程失败:', error);\r\n    return json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    }, { status: 500 });\r\n  }\r\n}", "import { Exa } from 'exa-js';\nimport puppeteer from 'puppeteer-extra';\nimport StealthPlugin from 'puppeteer-extra-plugin-stealth';\nimport OpenAI from 'openai';\n\n// 添加stealth插件\npuppeteer.use(StealthPlugin());\n\n// 类型定义\nexport interface PropertySearchResult {\n  success: boolean;\n  data?: PropertyData;\n  error?: string;\n}\n\nexport interface PropertyData {\n  id: string;\n  address: string;\n  searchDate: string;\n  totalSources: number;\n  successfulSources: number;\n  properties: PropertyInfo[];\n}\n\nexport interface PropertyInfo {\n  siteName: string;\n  address: string;\n  propertyType: string;\n  bedrooms: string;\n  bathrooms: string;\n  parking: string;\n  landSize: string;\n  buildingSize: string;\n  yearBuilt: string;\n  currentGuidePrice: string;\n  estimatedValueRange: string;\n  estimatedValueMid: string;\n  auctionDate: string;\n  inspectionTimes: string[];\n  historyRecords: any[];\n  description: string;\n  features: string[];\n  contact: string;\n  sourceUrl: string;\n}\n\ninterface SearchResult {\n  title: string;\n  url: string;\n  text?: string;\n  score?: number;\n}\n\n// 环境变量检查\nfunction checkEnvironmentVariables() {\n  const requiredVars = ['EXA_API_KEY', 'OPENAI_API_KEY'];\n  const missing = requiredVars.filter(varName => !process.env[varName]);\n  \n  if (missing.length > 0) {\n    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}。请检查 .env 文件是否正确配置。`);\n  }\n}\n\n// 初始化客户端\nconst exa = new Exa(process.env.EXA_API_KEY!);\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY!\n});\n\n/**\n * 主要的房产搜索函数 - 集成3个核心功能\n * 1. 通过EXA搜索，返回5条结果\n * 2. 站点去重+并发访问+LLM解析\n * 3. 汇总并返回数据\n */\nexport async function searchPropertyInfo(address: string): Promise<PropertySearchResult> {\n  try {\n    console.log(`🔍 开始搜索房产信息: ${address}`);\n    \n    // 检查环境变量\n    checkEnvironmentVariables();\n    \n    // 功能1: EXA搜索\n    const searchResults = await searchWithExa(address);\n    if (searchResults.length === 0) {\n      throw new Error('EXA搜索未找到相关结果');\n    }\n\n    console.log('searchResults:', searchResults);\n    \n    // 功能2: 站点去重+并发访问+LLM解析\n    const scrapedData = await scrapePropertyDetails(searchResults);\n    \n    // 功能3: 汇总并返回数据\n    const finalData = processAndReturnData(address, searchResults, scrapedData);\n    \n    console.log(`✅ 搜索完成，成功处理 ${finalData.successfulSources} 个网站`);\n    \n    return {\n      success: true,\n      data: finalData\n    };\n    \n  } catch (error) {\n    console.error('❌ 房产搜索失败:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : String(error)\n    };\n  }\n}\n\n/**\n * 功能1: 通过EXA搜索，返回5条结果\n */\nasync function searchWithExa(address: string, options = {}): Promise<SearchResult[]> {\n  console.log('📡 执行EXA搜索...');\n\n  // 默认选项\n  const {\n    numResults = 5,\n    useAutoprompt = false,\n    type = 'keyword',\n    includeDomains = [\n        'domain.com.au',\n        'realestate.com.au', \n        'realtor.com',\n        'view.com.au',\n        'onthehouse.com.au',\n        'propertyvalue.com.au',\n        'reiwa.com.au',\n        'allhomes.com.au',\n        'findbesthouse.com'\n    ],\n    excludeDomains = [],\n    startCrawlDate = null,\n    endCrawlDate = null,\n    includeText = [],\n    excludeText = [],\n    context = false\n  } = options;\n  \n  const searchQuery = address;\n  \n  // // 使用正确的EXA API调用方式\n  // const searchResults = await exa.searchAndContents(searchQuery, {\n  //   numResults: 5,\n  //   useAutoprompt: true,\n  //   type: 'neural',\n  //   includeDomains: [\n  //     'domain.com.au',\n  //     'realestate.com.au', \n  //     'realtor.com',\n  //     'view.com.au',\n  //     'onthehouse.com.au',\n  //     'propertyvalue.com.au',\n  //     'reiwa.com.au',\n  //     'allhomes.com.au'\n  //   ]\n  // });\n  \n  // console.log('searchResults.results:', searchResults.results);\n  // console.log(`✅ EXA搜索完成，找到 ${searchResults.results.length} 个结果`);\n\n  // 构建搜索参数\n  const searchParams = {\n      query: searchQuery,\n      numResults: numResults,\n      useAutoprompt: useAutoprompt,\n      type: type,\n      includeDomains,\n      excludeDomains,\n      startCrawlDate,\n      endCrawlDate,\n      includeText,\n      excludeText,\n      context\n  };\n\n  console.log('searchQuery:', searchQuery);\n  console.log('searchParams:', searchParams);\n  const searchResults = await exa.search(searchQuery, searchParams);\n  \n  return searchResults.results.map((result: any) => ({\n    title: result.title,\n    url: result.url,\n    text: result.text,\n    score: result.score\n  }));\n}\n\n/**\n * 功能2: 站点去重+并发访问+LLM解析\n */\nasync function scrapePropertyDetails(searchResults: SearchResult[]): Promise<PropertyInfo[]> {\n  console.log('🕷️ 开始抓取房产详情...');\n  \n  // 站点去重\n  const filteredResults = filterBySite(searchResults);\n  console.log(`📋 去重后保留 ${filteredResults.length} 个网站`);\n  \n  // 启动浏览器\n  const browser = await puppeteer.launch({\n    headless: true,\n    args: [\n      '--no-sandbox',\n      '--disable-setuid-sandbox',\n      '--disable-dev-shm-usage',\n      '--disable-web-security'\n    ]\n  });\n  \n  try {\n    // 并发处理（限制并发数为3）\n    const CONCURRENT_LIMIT = 5;\n    const results: PropertyInfo[] = [];\n    \n    for (let i = 0; i < filteredResults.length; i += CONCURRENT_LIMIT) {\n      const batch = filteredResults.slice(i, i + CONCURRENT_LIMIT);\n      \n      const batchPromises = batch.map(async (result) => {\n        try {\n          return await scrapePropertyPage(browser, result);\n        } catch (error) {\n          console.log(`❌ 抓取失败: ${result.title} - ${error}`);\n          return null;\n        }\n      });\n      \n      const batchResults = await Promise.all(batchPromises);\n      results.push(...batchResults.filter(r => r !== null) as PropertyInfo[]);\n      \n      // 批次间延迟\n      if (i + CONCURRENT_LIMIT < filteredResults.length) {\n        await new Promise(resolve => setTimeout(resolve, 2000));\n      }\n    }\n    \n    return results;\n    \n  } finally {\n    await browser.close();\n  }\n}\n\n/**\n * 站点去重 - 每个网站只保留第一个链接\n */\nfunction filterBySite(searchResults: SearchResult[]): SearchResult[] {\n  const siteSeen = new Set<string>();\n  const filtered: SearchResult[] = [];\n  \n  for (const result of searchResults) {\n    const siteName = getSiteName(result.url);\n    if (!siteSeen.has(siteName)) {\n      siteSeen.add(siteName);\n      filtered.push(result);\n    }\n  }\n  \n  return filtered;\n}\n\n/**\n * 获取网站名称\n */\nfunction getSiteName(url: string): string {\n  if (url.includes('domain.com.au')) return 'Domain';\n  if (url.includes('realestate.com.au')) return 'RealEstate.com.au';\n  if (url.includes('realtor.com')) return 'Realtor.com';\n  if (url.includes('view.com.au')) return 'View.com.au';\n  if (url.includes('onthehouse.com.au')) return 'OnTheHouse.com.au';\n  if (url.includes('propertyvalue.com.au')) return 'PropertyValue.com.au';\n  return 'Unknown';\n}\n\n/**\n * 抓取单个房产页面\n */\nasync function scrapePropertyPage(browser: any, searchResult: SearchResult): Promise<PropertyInfo | null> {\n  const page = await browser.newPage();\n\n  try {\n    // 设置隐身模式\n    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');\n    await page.setExtraHTTPHeaders({\n      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',\n      'Accept-Language': 'en-US,en;q=0.9',\n      'Accept-Encoding': 'gzip, deflate, br',\n      'DNT': '1'\n    });\n\n    // 访问页面\n    await page.goto(searchResult.url, {\n      waitUntil: 'domcontentloaded',\n      timeout: 30000\n    });\n\n    // 等待页面加载\n    await new Promise(resolve => setTimeout(resolve, 3000));\n\n    // 获取页面HTML内容\n    const htmlContent = await page.content();\n\n    // 使用LLM解析内容\n    const propertyData = await extractDataWithLLM(htmlContent, searchResult.url);\n\n    return propertyData;\n\n  } catch (error) {\n    console.log(`抓取页面失败: ${searchResult.url} - ${error}`);\n    return null;\n  } finally {\n    await page.close();\n  }\n}\n\n/**\n * 使用LLM解析HTML内容\n */\nasync function extractDataWithLLM(htmlContent: string, url: string): Promise<PropertyInfo> {\n  const siteName = getSiteName(url);\n\n  const prompt = `你是一个房产信息提取专家。请分析以下HTML内容，提取房产信息。\n\n**重要**: 你必须只返回JSON格式的数据，不要添加任何其他文字说明。\n\n从HTML中提取以下信息，如果找不到就写\"N/A\"。\n\n返回格式示例：\n{\n  \"siteName\": \"${siteName}\",\n  \"address\": \"完整地址\",\n  \"propertyType\": \"房产类型\",\n  \"bedrooms\": \"卧室数量\",\n  \"bathrooms\": \"浴室数量\",\n  \"parking\": \"停车位数量\",\n  \"landSize\": \"土地面积\",\n  \"buildingSize\": \"建筑面积\",\n  \"yearBuilt\": \"建成年份\",\n  \"currentGuidePrice\": \"当前guide价格\",\n  \"estimatedValueRange\": \"估价范围\",\n  \"estimatedValueMid\": \"估价中位数\",\n  \"auctionDate\": \"拍卖日期和时间\",\n  \"inspectionTimes\": [\"开放检查时间1\", \"开放检查时间2\"],\n  \"historyRecords\": [\n    {\n      \"type\": \"listing|sale\",\n      \"date\": \"日期\",\n      \"price\": \"价格或guide价格\",\n      \"details\": \"详细信息\"\n    }\n  ],\n  \"description\": \"房产简短描述\",\n  \"features\": [\"特色1\", \"特色2\"],\n  \"contact\": \"联系方式\"\n}\n\nHTML内容：\n${htmlContent.substring(0, 30000)}`;\n\n  try {\n    const response = await openai.chat.completions.create({\n      model: process.env.MODEL_NAME || 'gpt-5-nano',\n      messages: [\n        {\n          role: 'user',\n          content: prompt\n        }\n      ]\n    });\n\n    const content = response.choices[0].message.content;\n\n    // 解析JSON响应\n    let propertyData;\n    const codeBlockMatch = content?.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n    let jsonText = null;\n\n    if (codeBlockMatch) {\n      jsonText = codeBlockMatch[1];\n    } else {\n      const jsonMatch = content?.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        jsonText = jsonMatch[0];\n      }\n    }\n\n    if (jsonText) {\n      try {\n        propertyData = JSON.parse(jsonText);\n        propertyData.sourceUrl = url;\n        return propertyData;\n      } catch (parseError) {\n        console.log('JSON解析失败，使用默认数据');\n      }\n    }\n\n    // 如果解析失败，返回默认数据\n    return createDefaultPropertyInfo(siteName, url);\n\n  } catch (error) {\n    console.log(`LLM处理错误: ${error}`);\n    return createDefaultPropertyInfo(siteName, url);\n  }\n}\n\n/**\n * 创建默认房产信息\n */\nfunction createDefaultPropertyInfo(siteName: string, url: string): PropertyInfo {\n  return {\n    siteName: siteName,\n    address: 'N/A',\n    propertyType: 'N/A',\n    bedrooms: 'N/A',\n    bathrooms: 'N/A',\n    parking: 'N/A',\n    landSize: 'N/A',\n    buildingSize: 'N/A',\n    yearBuilt: 'N/A',\n    currentGuidePrice: 'N/A',\n    estimatedValueRange: 'N/A',\n    estimatedValueMid: 'N/A',\n    auctionDate: 'N/A',\n    inspectionTimes: [],\n    historyRecords: [],\n    description: 'N/A',\n    features: [],\n    contact: 'N/A',\n    sourceUrl: url\n  };\n}\n\n/**\n * 功能3: 汇总并返回数据\n */\nfunction processAndReturnData(address: string, searchResults: SearchResult[], scrapedData: PropertyInfo[]): PropertyData {\n  return {\n    id: `property-${Date.now()}`,\n    address: address,\n    searchDate: new Date().toISOString(),\n    totalSources: searchResults.length,\n    successfulSources: scrapedData.length,\n    properties: scrapedData\n  };\n}\n", "import { useState, useEffect } from \"react\";\r\nimport { localStorageUtils, clipboardUtils, type PropertyData } from \"~/utils/property-client\";\r\nimport SearchButton from \"~/components/SearchButton\";\r\nimport ErrorMessage from \"~/components/ErrorMessage\";\r\nimport PropertyCard from \"~/components/PropertyCard\";\r\n\r\nexport default function Index() {\r\n  const [properties, setProperties] = useState<PropertyData[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 从localStorage加载数据\r\n  useEffect(() => {\r\n    const savedProperties = localStorageUtils.loadProperties();\r\n    setProperties(savedProperties);\r\n  }, []);\r\n\r\n  // 保存到localStorage\r\n  useEffect(() => {\r\n    localStorageUtils.saveProperties(properties);\r\n  }, [properties]);\r\n\r\n  const handleSearchClick = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // 读取剪贴板内容\r\n      const clipboardText = await clipboardUtils.readText();\r\n\r\n      if (!clipboardText.trim()) {\r\n        throw new Error('剪贴板内容为空，请先复制房产地址');\r\n      }\r\n\r\n      console.log('剪贴板内容:', clipboardText);\r\n\r\n      // 调用API进行搜索和抓取\r\n      const response = await fetch('/api/search-property', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ address: clipboardText.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || `API调用失败: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.success && result.data) {\r\n        // 添加新的房产数据到列表开头\r\n        setProperties(prev => [result.data, ...prev]);\r\n      } else {\r\n        throw new Error(result.error || '搜索失败');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('搜索失败:', error);\r\n      setError(error instanceof Error ? error.message : String(error));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteProperty = (index: number) => {\r\n    setProperties(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const containerStyle = {\r\n    maxWidth: '1200px',\r\n    margin: '0 auto',\r\n    padding: '20px',\r\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\r\n    minHeight: '100vh',\r\n    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',\r\n  };\r\n\r\n  return (\r\n    <div style={containerStyle}>\r\n      {/* 头部区域 */}\r\n      <div style={{ textAlign: 'center', marginBottom: '40px' }}>\r\n        <h1 style={{\r\n          color: '#1e293b',\r\n          marginBottom: '12px',\r\n          fontSize: '28px',\r\n          fontWeight: '800',\r\n          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\r\n          WebkitBackgroundClip: 'text',\r\n          WebkitTextFillColor: 'transparent',\r\n          backgroundClip: 'text'\r\n        }}>\r\n          🏠 房产信息搜索\r\n        </h1>\r\n        <p style={{\r\n          color: '#64748b',\r\n          fontSize: '16px',\r\n          lineHeight: '1.5',\r\n          maxWidth: '400px',\r\n          margin: '0 auto'\r\n        }}>\r\n          复制房产地址到剪贴板，然后点击按钮开始搜索\r\n        </p>\r\n      </div>\r\n\r\n      {/* 搜索按钮区域 */}\r\n      <div style={{ textAlign: 'center', marginBottom: '40px' }}>\r\n        <SearchButton\r\n          isLoading={isLoading}\r\n          onClick={handleSearchClick}\r\n        />\r\n\r\n        {error && <ErrorMessage error={error} />}\r\n      </div>\r\n\r\n      {/* 结果列表 */}\r\n      {properties.length > 0 && (\r\n        <div>\r\n          <div style={{\r\n            display: 'flex',\r\n            justifyContent: 'space-between',\r\n            alignItems: 'center',\r\n            marginBottom: '24px',\r\n            padding: '0 4px'\r\n          }}>\r\n            <h2 style={{\r\n              color: '#1e293b',\r\n              margin: '0',\r\n              fontSize: '20px',\r\n              fontWeight: '700'\r\n            }}>\r\n              搜索结果\r\n            </h2>\r\n            <span style={{\r\n              background: '#3b82f6',\r\n              color: 'white',\r\n              padding: '4px 12px',\r\n              borderRadius: '20px',\r\n              fontSize: '14px',\r\n              fontWeight: '600'\r\n            }}>\r\n              {properties.length} 条记录\r\n            </span>\r\n          </div>\r\n\r\n          {properties.map((property, index) => (\r\n            <PropertyCard\r\n              key={property.id}\r\n              property={property}\r\n              onDelete={() => handleDeleteProperty(index)}\r\n            />\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* 空状态 */}\r\n      {properties.length === 0 && !isLoading && (\r\n        <div style={{\r\n          textAlign: 'center',\r\n          padding: '60px 20px',\r\n          color: '#64748b'\r\n        }}>\r\n          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🏡</div>\r\n          <h3 style={{\r\n            margin: '0 0 8px 0',\r\n            color: '#475569',\r\n            fontSize: '18px',\r\n            fontWeight: '600'\r\n          }}>\r\n            还没有搜索记录\r\n          </h3>\r\n          <p style={{ margin: '0', fontSize: '14px' }}>\r\n            复制房产地址并点击搜索按钮开始使用\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}", "// 客户端工具函数和类型定义\r\n\r\nexport interface PropertySearchResult {\r\n  success: boolean;\r\n  data?: PropertyData;\r\n  error?: string;\r\n}\r\n\r\nexport interface PropertyData {\r\n  id: string;\r\n  address: string;\r\n  searchDate: string;\r\n  totalSources: number;\r\n  successfulSources: number;\r\n  properties: PropertyInfo[];\r\n}\r\n\r\nexport interface PropertyInfo {\r\n  siteName: string;\r\n  address: string;\r\n  propertyType: string;\r\n  bedrooms: string;\r\n  bathrooms: string;\r\n  parking: string;\r\n  landSize: string;\r\n  buildingSize: string;\r\n  yearBuilt: string;\r\n  currentGuidePrice: string;\r\n  estimatedValueRange: string;\r\n  estimatedValueMid: string;\r\n  auctionDate: string;\r\n  inspectionTimes: string[];\r\n  historyRecords: any[];\r\n  description: string;\r\n  features: string[];\r\n  contact: string;\r\n  sourceUrl: string;\r\n}\r\n\r\n// 本地存储工具函数\r\nexport const localStorageUtils = {\r\n  saveProperties: (properties: PropertyData[]) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('propertySearchResults', JSON.stringify(properties));\r\n    }\r\n  },\r\n\r\n  loadProperties: (): PropertyData[] => {\r\n    if (typeof window !== 'undefined') {\r\n      const saved = localStorage.getItem('propertySearchResults');\r\n      return saved ? JSON.parse(saved) : [];\r\n    }\r\n    return [];\r\n  },\r\n\r\n  removeProperty: (index: number) => {\r\n    if (typeof window !== 'undefined') {\r\n      const properties = localStorageUtils.loadProperties();\r\n      properties.splice(index, 1);\r\n      localStorageUtils.saveProperties(properties);\r\n      return properties;\r\n    }\r\n    return [];\r\n  }\r\n};\r\n\r\n// 剪贴板工具函数\r\nexport const clipboardUtils = {\r\n  readText: async (): Promise<string> => {\r\n    if (typeof window !== 'undefined' && navigator.clipboard) {\r\n      try {\r\n        return await navigator.clipboard.readText();\r\n      } catch (error) {\r\n        console.error('无法读取剪贴板:', error);\r\n        throw new Error('无法访问剪贴板，请检查浏览器权限');\r\n      }\r\n    }\r\n    throw new Error('剪贴板API不可用');\r\n  }\r\n};", "interface SearchButtonProps {\n  isLoading: boolean;\n  onClick: () => void;\n}\n\nexport default function SearchButton({ isLoading, onClick }: SearchButtonProps) {\n  const buttonStyle = {\n    background: isLoading \n      ? 'linear-gradient(135deg, #94a3b8 0%, #64748b 100%)'\n      : 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n    color: 'white',\n    border: 'none',\n    padding: '16px 32px',\n    fontSize: '18px',\n    fontWeight: '600',\n    borderRadius: '12px',\n    cursor: isLoading ? 'not-allowed' : 'pointer',\n    boxShadow: isLoading \n      ? '0 4px 15px rgba(0,0,0,0.1)' \n      : '0 6px 20px rgba(59, 130, 246, 0.3)',\n    minWidth: '280px',\n    transition: 'all 0.3s ease',\n    transform: isLoading ? 'scale(0.98)' : 'scale(1)',\n  };\n\n  return (\n    <button \n      style={buttonStyle}\n      onClick={onClick}\n      disabled={isLoading}\n    >\n      {isLoading ? (\n        <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>\n          <span style={{ \n            width: '20px', \n            height: '20px', \n            border: '2px solid #ffffff40',\n            borderTop: '2px solid #ffffff',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }}></span>\n          搜索中...\n        </span>\n      ) : (\n        <span style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>\n          📋 从剪贴板搜索\n        </span>\n      )}\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </button>\n  );\n}\n", "interface ErrorMessageProps {\n  error: string;\n}\n\nexport default function ErrorMessage({ error }: ErrorMessageProps) {\n  const errorStyle = {\n    background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',\n    color: '#dc2626',\n    padding: '16px',\n    borderRadius: '12px',\n    marginTop: '16px',\n    border: '1px solid #fecaca',\n    boxShadow: '0 4px 12px rgba(220, 38, 38, 0.1)',\n    fontSize: '14px',\n    lineHeight: '1.5',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n  };\n\n  return (\n    <div style={errorStyle}>\n      <span style={{ fontSize: '18px' }}>⚠️</span>\n      <span>{error}</span>\n    </div>\n  );\n}\n", "import type { PropertyData } from \"~/utils/property-client\";\n\ninterface PropertyCardProps {\n  property: PropertyData;\n  onDelete: () => void;\n}\n\nexport default function PropertyCard({ property, onDelete }: PropertyCardProps) {\n  const cardStyle = {\n    background: 'white',\n    borderRadius: '16px',\n    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n    marginBottom: '24px',\n    overflow: 'hidden' as const,\n    border: '1px solid #f1f5f9',\n  };\n\n  const headerStyle = {\n    background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n    padding: '20px',\n    borderBottom: '1px solid #e2e8f0',\n  };\n\n  const deleteButtonStyle = {\n    background: '#ef4444',\n    color: 'white',\n    border: 'none',\n    padding: '8px 12px',\n    borderRadius: '8px',\n    cursor: 'pointer',\n    fontSize: '12px',\n    fontWeight: '500',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '4px',\n  };\n\n  const tableStyle = {\n    width: '100%',\n    borderCollapse: 'collapse' as const,\n    fontSize: '14px',\n  };\n\n  const thStyle = {\n    padding: '12px 8px',\n    textAlign: 'left' as const,\n    borderBottom: '2px solid #e2e8f0',\n    background: '#f8fafc',\n    fontWeight: '600',\n    color: '#475569',\n    fontSize: '13px',\n  };\n\n  const tdStyle = {\n    padding: '12px 8px',\n    textAlign: 'left' as const,\n    borderBottom: '1px solid #f1f5f9',\n    maxWidth: '120px',\n    overflow: 'hidden' as const,\n    textOverflow: 'ellipsis',\n    fontSize: '13px',\n    color: '#334155',\n  };\n\n  return (\n    <div style={cardStyle}>\n      <div style={headerStyle}>\n        <h3 style={{ \n          margin: '0 0 12px 0', \n          color: '#1e293b', \n          fontSize: '18px',\n          fontWeight: '700',\n          lineHeight: '1.3'\n        }}>\n          📍 {property.address}\n        </h3>\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          fontSize: '13px', \n          color: '#64748b',\n          flexWrap: 'wrap',\n          gap: '8px'\n        }}>\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>\n            <span>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>\n            <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>\n          </div>\n          <button \n            style={deleteButtonStyle}\n            onClick={onDelete}\n            title=\"删除这条记录\"\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = '#dc2626';\n              e.currentTarget.style.transform = 'scale(1.05)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.background = '#ef4444';\n              e.currentTarget.style.transform = 'scale(1)';\n            }}\n          >\n            🗑️ 删除\n          </button>\n        </div>\n      </div>\n\n      <div style={{ overflowX: 'auto', padding: '0' }}>\n        <table style={tableStyle}>\n          <thead>\n            <tr>\n              <th style={thStyle}>网站</th>\n              <th style={thStyle}>房型</th>\n              <th style={thStyle}>卧室</th>\n              <th style={thStyle}>浴室</th>\n              <th style={thStyle}>停车</th>\n              <th style={thStyle}>建成年份</th>\n              <th style={thStyle}>指导价</th>\n              <th style={thStyle}>估价范围</th>\n              <th style={thStyle}>拍卖日期</th>\n            </tr>\n          </thead>\n          <tbody>\n            {property.properties.map((prop, propIndex) => (\n              <tr key={propIndex} style={{ \n                transition: 'background-color 0.2s ease',\n              }}>\n                <td style={tdStyle}>\n                  <a \n                    href={prop.sourceUrl} \n                    target=\"_blank\" \n                    rel=\"noopener noreferrer\" \n                    style={{ \n                      color: '#3b82f6', \n                      textDecoration: 'none',\n                      fontWeight: '500',\n                      borderBottom: '1px solid transparent',\n                      transition: 'border-color 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.borderBottomColor = '#3b82f6';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.borderBottomColor = 'transparent';\n                    }}\n                  >\n                    {prop.siteName}\n                  </a>\n                </td>\n                <td style={tdStyle}>{prop.propertyType}</td>\n                <td style={tdStyle}>{prop.bedrooms}</td>\n                <td style={tdStyle}>{prop.bathrooms}</td>\n                <td style={tdStyle}>{prop.parking}</td>\n                <td style={tdStyle}>{prop.yearBuilt}</td>\n                <td style={tdStyle}>{prop.currentGuidePrice}</td>\n                <td style={tdStyle}>{prop.estimatedValueRange}</td>\n                <td style={tdStyle}>{prop.auctionDate}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n}\n", "export default {'entry':{'module':'/build/entry.client-HJQELB22.js','imports':['/build/_shared/chunk-O4BRYNJ4.js','/build/_shared/chunk-V3BJQ67B.js','/build/_shared/chunk-U4FRFQSK.js','/build/_shared/chunk-XGOTYLZ5.js','/build/_shared/chunk-X3T7OMQU.js','/build/_shared/chunk-UWV35TSL.js','/build/_shared/chunk-7M6SC7J5.js','/build/_shared/chunk-PNG5AS42.js']},'routes':{'root':{'id':'root','parentId':undefined,'path':'','index':undefined,'caseSensitive':undefined,'module':'/build/root-7CUIXVDC.js','imports':undefined,'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/_index':{'id':'routes/_index','parentId':'root','path':undefined,'index':true,'caseSensitive':undefined,'module':'/build/routes/_index-AOPVVPKB.js','imports':undefined,'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/api.search-property':{'id':'routes/api.search-property','parentId':'root','path':'api/search-property','index':undefined,'caseSensitive':undefined,'module':'/build/routes/api.search-property-JHHPWYLI.js','imports':undefined,'hasAction':true,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false}},'version':'2b82642f','hmr':{'runtime':'/build/_shared\\\\chunk-X3T7OMQU.js','timestamp':1758972219135},'url':'/build/manifest-2B82642F.js'};", "\nimport * as entryServer from \"C:\\\\Users\\\\<USER>\\\\projects\\\\property_helper\\\\app\\\\entry.server.tsx\";\nimport * as route0 from \"./root.tsx\";\nimport * as route1 from \"./routes/api.search-property.ts\";\nimport * as route2 from \"./routes/_index.tsx\";\n  export const mode = \"development\";\n  export { default as assets } from \"@remix-run/dev/assets-manifest\";\n  export const assetsBuildDirectory = \"public\\\\build\";\n  export const future = {\"v3_fetcherPersist\":false,\"v3_relativeSplatPath\":false,\"v3_throwAbortReason\":false,\"v3_routeConfig\":false,\"v3_singleFetch\":false,\"v3_lazyRouteDiscovery\":false,\"unstable_optimizeDeps\":false};\n  export const publicPath = \"/build/\";\n  export const entry = { module: entryServer };\n  export const routes = {\n    \"root\": {\n      id: \"root\",\n      parentId: undefined,\n      path: \"\",\n      index: undefined,\n      caseSensitive: undefined,\n      module: route0\n    },\n  \"routes/api.search-property\": {\n      id: \"routes/api.search-property\",\n      parentId: \"root\",\n      path: \"api/search-property\",\n      index: undefined,\n      caseSensitive: undefined,\n      module: route1\n    },\n  \"routes/_index\": {\n      id: \"routes/_index\",\n      parentId: \"root\",\n      path: undefined,\n      index: true,\n      caseSensitive: undefined,\n      module: route2\n    }\n  };"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AACA,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AAS3B;AAPW,SAAR,cACL,SACA,oBACA,iBACA,cACA;AACA,MAAM,SAAS;AAAA,IACb,uBAAC,eAAY,SAAS,cAAc,KAAK,QAAQ,OAAjD;AAAA;AAAA;AAAA;AAAA,WAAsD;AAAA,EACxD;AAEA,yBAAgB,IAAI,gBAAgB,WAAW,GAExC,IAAI,SAAS,oBAAoB,QAAQ;AAAA,IAC9C,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACH;;;ACpBA;AAAA;AAAA;AAAA;AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAMC,mBAAAA,eAAA;AAJO,SAAR,MAAuB;AAC5B,SACE,gBAAAA,QAAC,UAAK,MAAK,SACT;AAAA,oBAAAA,QAAC,UACC;AAAA,sBAAAA,QAAC,UAAK,SAAQ,WAAd;AAAA;AAAA;AAAA;AAAA,aAAsB;AAAA,MACtB,gBAAAA,QAAC,UAAK,MAAK,YAAW,SAAQ,yCAA9B;AAAA;AAAA;AAAA;AAAA,aAAoE;AAAA,MACpE,gBAAAA,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA,aAAM;AAAA,MACN,gBAAAA,QAAC,WAAD;AAAA;AAAA;AAAA;AAAA,aAAO;AAAA,MACP,gBAAAA,QAAC,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAR;AAAA;AAAA;AAAA;AAAA,aA+FE;AAAA,SApGJ;AAAA;AAAA;AAAA;AAAA,WAqGA;AAAA,IACA,gBAAAA,QAAC,UACC;AAAA,sBAAAA,QAAC,YAAD;AAAA;AAAA;AAAA;AAAA,aAAQ;AAAA,MACR,gBAAAA,QAAC,uBAAD;AAAA;AAAA;AAAA;AAAA,aAAmB;AAAA,MACnB,gBAAAA,QAAC,aAAD;AAAA;AAAA;AAAA;AAAA,aAAS;AAAA,MACT,gBAAAA,QAAC,gBAAD;AAAA;AAAA;AAAA;AAAA,aAAY;AAAA,SAJd;AAAA;AAAA;AAAA;AAAA,WAKA;AAAA,OA5GF;AAAA;AAAA;AAAA;AAAA,SA6GA;AAEJ;;;AC1HA;AAAA;AAAA;AAAA;AAAA,SAAS,YAAY;;;ACArB,SAAS,WAAW;AACpB,OAAO,eAAe;AACtB,OAAO,mBAAmB;AAC1B,OAAO,YAAY;AAGnB,UAAU,IAAI,cAAc,CAAC;AAgD7B,SAAS,4BAA4B;AAEnC,MAAM,UADe,CAAC,eAAe,gBAAgB,EACxB,OAAO,aAAW,CAAC,QAAQ,IAAI,OAAO,CAAC;AAEpE,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,MAAM,2DAAc,QAAQ,KAAK,IAAI,uFAAsB;AAEzE;AAGA,IAAM,MAAM,IAAI,IAAI,QAAQ,IAAI,WAAY,GACtC,SAAS,IAAI,OAAO;AAAA,EACxB,QAAQ,QAAQ,IAAI;AACtB,CAAC;AAQD,eAAsB,mBAAmB,SAAgD;AACvF,MAAI;AACF,YAAQ,IAAI,+DAAgB,SAAS,GAGrC,0BAA0B;AAG1B,QAAM,gBAAgB,MAAM,cAAc,OAAO;AACjD,QAAI,cAAc,WAAW;AAC3B,YAAM,IAAI,MAAM,2DAAc;AAGhC,YAAQ,IAAI,kBAAkB,aAAa;AAG3C,QAAM,cAAc,MAAM,sBAAsB,aAAa,GAGvD,YAAY,qBAAqB,SAAS,eAAe,WAAW;AAE1E,mBAAQ,IAAI,iEAAe,UAAU,sCAAuB,GAErD;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EAEF,SAAS,OAAP;AACA,mBAAQ,MAAM,gDAAa,KAAK,GACzB;AAAA,MACL,SAAS;AAAA,MACT,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,IAC9D;AAAA,EACF;AACF;AAKA,eAAe,cAAc,SAAiB,UAAU,CAAC,GAA4B;AACnF,UAAQ,IAAI,0CAAe;AAG3B,MAAM;AAAA,IACJ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,OAAO;AAAA,IACP,iBAAiB;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,iBAAiB,CAAC;AAAA,IAClB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc,CAAC;AAAA,IACf,cAAc,CAAC;AAAA,IACf,UAAU;AAAA,EACZ,IAAI,SAEE,cAAc,SAuBd,eAAe;AAAA,IACjB,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,iBAAQ,IAAI,gBAAgB,WAAW,GACvC,QAAQ,IAAI,iBAAiB,YAAY,IACnB,MAAM,IAAI,OAAO,aAAa,YAAY,GAE3C,QAAQ,IAAI,CAAC,YAAiB;AAAA,IACjD,OAAO,OAAO;AAAA,IACd,KAAK,OAAO;AAAA,IACZ,MAAM,OAAO;AAAA,IACb,OAAO,OAAO;AAAA,EAChB,EAAE;AACJ;AAKA,eAAe,sBAAsB,eAAwD;AAC3F,UAAQ,IAAI,qEAAiB;AAG7B,MAAM,kBAAkB,aAAa,aAAa;AAClD,UAAQ,IAAI,4CAAY,gBAAgB,2BAAY;AAGpD,MAAM,UAAU,MAAM,UAAU,OAAO;AAAA,IACrC,UAAU;AAAA,IACV,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI;AAGF,QAAM,UAA0B,CAAC;AAEjC,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK,GAAkB;AAGjE,UAAM,gBAFQ,gBAAgB,MAAM,GAAG,IAAI,CAAgB,EAE/B,IAAI,OAAO,WAAW;AAChD,YAAI;AACF,iBAAO,MAAM,mBAAmB,SAAS,MAAM;AAAA,QACjD,SAAS,OAAP;AACA,yBAAQ,IAAI,oCAAW,OAAO,WAAW,OAAO,GACzC;AAAA,QACT;AAAA,MACF,CAAC,GAEK,eAAe,MAAM,QAAQ,IAAI,aAAa;AACpD,cAAQ,KAAK,GAAG,aAAa,OAAO,OAAK,MAAM,IAAI,CAAmB,GAGlE,IAAI,IAAmB,gBAAgB,UACzC,MAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAAA;AAI1D,WAAO;AAAA,EAET,UAAE;AACA,UAAM,QAAQ,MAAM;AAAA,EACtB;AACF;AAKA,SAAS,aAAa,eAA+C;AACnE,MAAM,WAAW,oBAAI,IAAY,GAC3B,WAA2B,CAAC;AAElC,WAAW,UAAU,eAAe;AAClC,QAAM,WAAW,YAAY,OAAO,GAAG;AACvC,IAAK,SAAS,IAAI,QAAQ,MACxB,SAAS,IAAI,QAAQ,GACrB,SAAS,KAAK,MAAM;AAAA;AAIxB,SAAO;AACT;AAKA,SAAS,YAAY,KAAqB;AACxC,SAAI,IAAI,SAAS,eAAe,IAAU,WACtC,IAAI,SAAS,mBAAmB,IAAU,sBAC1C,IAAI,SAAS,aAAa,IAAU,gBACpC,IAAI,SAAS,aAAa,IAAU,gBACpC,IAAI,SAAS,mBAAmB,IAAU,sBAC1C,IAAI,SAAS,sBAAsB,IAAU,yBAC1C;AACT;AAKA,eAAe,mBAAmB,SAAc,cAA0D;AACxG,MAAM,OAAO,MAAM,QAAQ,QAAQ;AAEnC,MAAI;AAEF,UAAM,KAAK,aAAa,iHAAiH,GACzI,MAAM,KAAK,oBAAoB;AAAA,MAC7B,QAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,KAAO;AAAA,IACT,CAAC,GAGD,MAAM,KAAK,KAAK,aAAa,KAAK;AAAA,MAChC,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC,GAGD,MAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,QAAM,cAAc,MAAM,KAAK,QAAQ;AAKvC,WAFqB,MAAM,mBAAmB,aAAa,aAAa,GAAG;AAAA,EAI7E,SAAS,OAAP;AACA,mBAAQ,IAAI,yCAAW,aAAa,SAAS,OAAO,GAC7C;AAAA,EACT,UAAE;AACA,UAAM,KAAK,MAAM;AAAA,EACnB;AACF;AAKA,eAAe,mBAAmB,aAAqB,KAAoC;AACzF,MAAM,WAAW,YAAY,GAAG,GAE1B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4Bf,YAAY,UAAU,GAAG,GAAK;AAE9B,MAAI;AAWF,QAAM,WAVW,MAAM,OAAO,KAAK,YAAY,OAAO;AAAA,MACpD,OAAO,QAAQ,IAAI,cAAc;AAAA,MACjC,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF,CAAC,GAEwB,QAAQ,CAAC,EAAE,QAAQ,SAGxC,cACE,iBAAiB,SAAS,MAAM,4BAA4B,GAC9D,WAAW;AAEf,QAAI;AACF,iBAAW,eAAe,CAAC;AAAA,SACtB;AACL,UAAM,YAAY,SAAS,MAAM,aAAa;AAC9C,MAAI,cACF,WAAW,UAAU,CAAC;AAAA;AAI1B,QAAI;AACF,UAAI;AACF,8BAAe,KAAK,MAAM,QAAQ,GAClC,aAAa,YAAY,KAClB;AAAA,MACT,QAAE;AACA,gBAAQ,IAAI,wEAAiB;AAAA,MAC/B;AAIF,WAAO,0BAA0B,UAAU,GAAG;AAAA,EAEhD,SAAS,OAAP;AACA,mBAAQ,IAAI,gCAAY,OAAO,GACxB,0BAA0B,UAAU,GAAG;AAAA,EAChD;AACF;AAKA,SAAS,0BAA0B,UAAkB,KAA2B;AAC9E,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,IACd,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,iBAAiB,CAAC;AAAA,IAClB,gBAAgB,CAAC;AAAA,IACjB,aAAa;AAAA,IACb,UAAU,CAAC;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF;AAKA,SAAS,qBAAqB,SAAiB,eAA+B,aAA2C;AACvH,SAAO;AAAA,IACL,IAAI,YAAY,KAAK,IAAI;AAAA,IACzB;AAAA,IACA,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,IACnC,cAAc,cAAc;AAAA,IAC5B,mBAAmB,YAAY;AAAA,IAC/B,YAAY;AAAA,EACd;AACF;;;AD1bA,eAAsB,OAAO,EAAE,QAAQ,GAAuB;AAC5D,MAAI,QAAQ,WAAW;AACrB,WAAO,KAAK,EAAE,OAAO,qBAAqB,GAAG,EAAE,QAAQ,IAAI,CAAC;AAG9D,MAAI;AACF,QAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAEvC,QAAI,CAAC,WAAW,OAAO,WAAY;AACjC,aAAO,KAAK,EAAE,OAAO,2BAA2B,GAAG,EAAE,QAAQ,IAAI,CAAC;AAGpE,YAAQ,IAAI,oEAAqB,SAAS;AAG1C,QAAM,SAAS,MAAM,mBAAmB,OAAO;AAE/C,WAAI,OAAO,UACF,KAAK,MAAM,IAEX,KAAK,QAAQ,EAAE,QAAQ,IAAI,CAAC;AAAA,EAGvC,SAAS,OAAP;AACA,mBAAQ,MAAM,qDAAkB,KAAK,GAC9B,KAAK;AAAA,MACV,SAAS;AAAA,MACT,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,IAC9D,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,EACpB;AACF;;;AElCA;AAAA;AAAA;AAAA;AAAA,SAAS,UAAU,iBAAiB;;;ACwC7B,IAAM,oBAAoB;AAAA,EAC/B,gBAAgB,CAAC,eAA+B;AAC9C,IAAI,OAAO,SAAW,OACpB,aAAa,QAAQ,yBAAyB,KAAK,UAAU,UAAU,CAAC;AAAA,EAE5E;AAAA,EAEA,gBAAgB,MAAsB;AACpC,QAAI,OAAO,SAAW,KAAa;AACjC,UAAM,QAAQ,aAAa,QAAQ,uBAAuB;AAC1D,aAAO,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA;AAEtC,WAAO,CAAC;AAAA,EACV;AAAA,EAEA,gBAAgB,CAAC,UAAkB;AACjC,QAAI,OAAO,SAAW,KAAa;AACjC,UAAM,aAAa,kBAAkB,eAAe;AACpD,wBAAW,OAAO,OAAO,CAAC,GAC1B,kBAAkB,eAAe,UAAU,GACpC;AAAA;AAET,WAAO,CAAC;AAAA,EACV;AACF,GAGa,iBAAiB;AAAA,EAC5B,UAAU,YAA6B;AACrC,QAAI,OAAO,SAAW,OAAe,UAAU;AAC7C,UAAI;AACF,eAAO,MAAM,UAAU,UAAU,SAAS;AAAA,MAC5C,SAAS,OAAP;AACA,sBAAQ,MAAM,+CAAY,KAAK,GACzB,IAAI,MAAM,kGAAkB;AAAA,MACpC;AAEF,UAAM,IAAI,MAAM,yCAAW;AAAA,EAC7B;AACF;;;AC9CU,mBAAAC,eAAA;AA5BK,SAAR,aAA8B,EAAE,WAAW,QAAQ,GAAsB;AAoB9E,SACE,gBAAAA;AAAA,IAAC;AAAA;AAAA,MACC,OArBgB;AAAA,QAClB,YAAY,YACR,sDACA;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,QAAQ,YAAY,gBAAgB;AAAA,QACpC,WAAW,YACP,+BACA;AAAA,QACJ,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW,YAAY,gBAAgB;AAAA,MACzC;AAAA,MAKI;AAAA,MACA,UAAU;AAAA,MAET;AAAA,oBACC,gBAAAA,QAAC,UAAK,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,gBAAgB,UAAU,KAAK,MAAM,GACzF;AAAA,0BAAAA,QAAC,UAAK,OAAO;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,WAAW;AAAA,YACX,cAAc;AAAA,YACd,WAAW;AAAA,UACb,KAPA;AAAA;AAAA;AAAA;AAAA,iBAOG;AAAA,UAAO;AAAA,aARZ;AAAA;AAAA;AAAA;AAAA,eAUA,IAEA,gBAAAA,QAAC,UAAK,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,gBAAgB,UAAU,KAAK,MAAM,GAAG,8DAA9F;AAAA;AAAA;AAAA;AAAA,eAEA;AAAA,QAEF,gBAAAA,QAAC,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA,WAAR;AAAA;AAAA;AAAA;AAAA,eAKE;AAAA;AAAA;AAAA,IA3BJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA;AAEJ;;;AClCM,mBAAAC,eAAA;AAlBS,SAAR,aAA8B,EAAE,MAAM,GAAsB;AAgBjE,SACE,gBAAAA,QAAC,SAAI,OAhBY;AAAA,IACjB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,KAAK;AAAA,EACP,GAII;AAAA,oBAAAA,QAAC,UAAK,OAAO,EAAE,UAAU,OAAO,GAAG,4BAAnC;AAAA;AAAA;AAAA;AAAA,WAAqC;AAAA,IACrC,gBAAAA,QAAC,UAAM,mBAAP;AAAA;AAAA;AAAA;AAAA,WAAa;AAAA,OAFf;AAAA;AAAA;AAAA;AAAA,SAGA;AAEJ;;;AC0CQ,mBAAAC,eAAA;AA7DO,SAAR,aAA8B,EAAE,UAAU,SAAS,GAAsB;AAC9E,MAAM,YAAY;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,GAEM,cAAc;AAAA,IAClB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,GAEM,oBAAoB;AAAA,IACxB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,KAAK;AAAA,EACP,GAEM,aAAa;AAAA,IACjB,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,UAAU;AAAA,EACZ,GAEM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,GAEM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AAEA,SACE,gBAAAA,QAAC,SAAI,OAAO,WACV;AAAA,oBAAAA,QAAC,SAAI,OAAO,aACV;AAAA,sBAAAA,QAAC,QAAG,OAAO;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,MACd,GAAG;AAAA;AAAA,QACG,SAAS;AAAA,WAPf;AAAA;AAAA;AAAA;AAAA,aAQA;AAAA,MACA,gBAAAA,QAAC,SAAI,OAAO;AAAA,QACV,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,KAAK;AAAA,MACP,GACE;AAAA,wBAAAA,QAAC,SAAI,OAAO,EAAE,SAAS,QAAQ,UAAU,QAAQ,KAAK,OAAO,GAC3D;AAAA,0BAAAA,QAAC,UAAK;AAAA;AAAA,YAAK,IAAI,KAAK,SAAS,UAAU,EAAE,eAAe,OAAO;AAAA,eAA/D;AAAA;AAAA;AAAA;AAAA,iBAAiE;AAAA,UACjE,gBAAAA,QAAC,UAAK;AAAA;AAAA,YAAI,SAAS;AAAA,YAAkB;AAAA,YAAE,SAAS;AAAA,YAAa;AAAA,eAA7D;AAAA;AAAA;AAAA;AAAA,iBAAiE;AAAA,aAFnE;AAAA;AAAA;AAAA;AAAA,eAGA;AAAA,QACA,gBAAAA;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,YACP,SAAS;AAAA,YACT,OAAM;AAAA,YACN,cAAc,CAAC,MAAM;AACnB,gBAAE,cAAc,MAAM,aAAa,WACnC,EAAE,cAAc,MAAM,YAAY;AAAA,YACpC;AAAA,YACA,cAAc,CAAC,MAAM;AACnB,gBAAE,cAAc,MAAM,aAAa,WACnC,EAAE,cAAc,MAAM,YAAY;AAAA,YACpC;AAAA,YACD;AAAA;AAAA,UAZD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcA;AAAA,WA3BF;AAAA;AAAA;AAAA;AAAA,aA4BA;AAAA,SAtCF;AAAA;AAAA;AAAA;AAAA,WAuCA;AAAA,IAEA,gBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,QAAQ,SAAS,IAAI,GAC5C,0BAAAA,QAAC,WAAM,OAAO,YACZ;AAAA,sBAAAA,QAAC,WACC,0BAAAA,QAAC,QACC;AAAA,wBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,QACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,QACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,QACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,QACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,QACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,eAAwB;AAAA,QACxB,gBAAAA,QAAC,QAAG,OAAO,SAAS,kCAApB;AAAA;AAAA;AAAA;AAAA,eAAuB;AAAA,QACvB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,eAAwB;AAAA,QACxB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,eAAwB;AAAA,WAT1B;AAAA;AAAA;AAAA;AAAA,aAUA,KAXF;AAAA;AAAA;AAAA;AAAA,aAYA;AAAA,MACA,gBAAAA,QAAC,WACE,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,QAAmB,OAAO;AAAA,QACzB,YAAY;AAAA,MACd,GACE;AAAA,wBAAAA,QAAC,QAAG,OAAO,SACT,0BAAAA;AAAA,UAAC;AAAA;AAAA,YACC,MAAM,KAAK;AAAA,YACX,QAAO;AAAA,YACP,KAAI;AAAA,YACJ,OAAO;AAAA,cACL,OAAO;AAAA,cACP,gBAAgB;AAAA,cAChB,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,YAAY;AAAA,YACd;AAAA,YACA,cAAc,CAAC,MAAM;AACnB,gBAAE,cAAc,MAAM,oBAAoB;AAAA,YAC5C;AAAA,YACA,cAAc,CAAC,MAAM;AACnB,gBAAE,cAAc,MAAM,oBAAoB;AAAA,YAC5C;AAAA,YAEC,eAAK;AAAA;AAAA,UAlBR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAmBA,KApBF;AAAA;AAAA;AAAA;AAAA,eAqBA;AAAA,QACA,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,gBAA1B;AAAA;AAAA;AAAA;AAAA,eAAuC;AAAA,QACvC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,YAA1B;AAAA;AAAA;AAAA;AAAA,eAAmC;AAAA,QACnC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,aAA1B;AAAA;AAAA;AAAA;AAAA,eAAoC;AAAA,QACpC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,WAA1B;AAAA;AAAA;AAAA;AAAA,eAAkC;AAAA,QAClC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,aAA1B;AAAA;AAAA;AAAA;AAAA,eAAoC;AAAA,QACpC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,qBAA1B;AAAA;AAAA;AAAA;AAAA,eAA4C;AAAA,QAC5C,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,uBAA1B;AAAA;AAAA;AAAA;AAAA,eAA8C;AAAA,QAC9C,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,eAA1B;AAAA;AAAA;AAAA;AAAA,eAAsC;AAAA,WAhC/B,WAAT;AAAA;AAAA;AAAA;AAAA,aAiCA,CACD,KApCH;AAAA;AAAA;AAAA;AAAA,aAqCA;AAAA,SAnDF;AAAA;AAAA;AAAA;AAAA,WAoDA,KArDF;AAAA;AAAA;AAAA;AAAA,WAsDA;AAAA,OAhGF;AAAA;AAAA;AAAA;AAAA,SAiGA;AAEJ;;;AJjFQ,mBAAAC,eAAA;AA9EO,SAAR,QAAyB;AAC9B,MAAM,CAAC,YAAY,aAAa,IAAI,SAAyB,CAAC,CAAC,GACzD,CAAC,WAAW,YAAY,IAAI,SAAS,EAAK,GAC1C,CAAC,OAAO,QAAQ,IAAI,SAAwB,IAAI;AAGtD,YAAU,MAAM;AACd,QAAM,kBAAkB,kBAAkB,eAAe;AACzD,kBAAc,eAAe;AAAA,EAC/B,GAAG,CAAC,CAAC,GAGL,UAAU,MAAM;AACd,sBAAkB,eAAe,UAAU;AAAA,EAC7C,GAAG,CAAC,UAAU,CAAC;AAEf,MAAM,oBAAoB,YAAY;AACpC,iBAAa,EAAI,GACjB,SAAS,IAAI;AAEb,QAAI;AAEF,UAAM,gBAAgB,MAAM,eAAe,SAAS;AAEpD,UAAI,CAAC,cAAc,KAAK;AACtB,cAAM,IAAI,MAAM,kGAAkB;AAGpC,cAAQ,IAAI,mCAAU,aAAa;AAGnC,UAAM,WAAW,MAAM,MAAM,wBAAwB;AAAA,QACnD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,SAAS,cAAc,KAAK,EAAE,CAAC;AAAA,MACxD,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,cAAM,IAAI,MAAM,UAAU,SAAS,gCAAY,SAAS,QAAQ;AAAA;AAGlE,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,UAAI,OAAO,WAAW,OAAO;AAE3B,sBAAc,UAAQ,CAAC,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA;AAE5C,cAAM,IAAI,MAAM,OAAO,SAAS,0BAAM;AAAA,IAG1C,SAASC,QAAP;AACA,cAAQ,MAAM,6BAASA,MAAK,GAC5B,SAASA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK,CAAC;AAAA,IACjE,UAAE;AACA,mBAAa,EAAK;AAAA,IACpB;AAAA,EACF,GAEM,uBAAuB,CAAC,UAAkB;AAC9C,kBAAc,UAAQ,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAC1D;AAWA,SACE,gBAAAD,QAAC,SAAI,OAVgB;AAAA,IACrB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,EACd,GAKI;AAAA,oBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,UAAU,cAAc,OAAO,GACtD;AAAA,sBAAAA,QAAC,QAAG,OAAO;AAAA,QACT,OAAO;AAAA,QACP,cAAc;AAAA,QACd,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,MAClB,GAAG,8DATH;AAAA;AAAA;AAAA;AAAA,aAWA;AAAA,MACA,gBAAAA,QAAC,OAAE,OAAO;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,QAAQ;AAAA,MACV,GAAG,8IANH;AAAA;AAAA;AAAA;AAAA,aAQA;AAAA,SArBF;AAAA;AAAA;AAAA;AAAA,WAsBA;AAAA,IAGA,gBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,UAAU,cAAc,OAAO,GACtD;AAAA,sBAAAA;AAAA,QAAC;AAAA;AAAA,UACC;AAAA,UACA,SAAS;AAAA;AAAA,QAFX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAGA;AAAA,MAEC,SAAS,gBAAAA,QAAC,gBAAa,SAAd;AAAA;AAAA;AAAA;AAAA,aAA4B;AAAA,SANxC;AAAA;AAAA;AAAA;AAAA,WAOA;AAAA,IAGC,WAAW,SAAS,KACnB,gBAAAA,QAAC,SACC;AAAA,sBAAAA,QAAC,SAAI,OAAO;AAAA,QACV,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,SAAS;AAAA,MACX,GACE;AAAA,wBAAAA,QAAC,QAAG,OAAO;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,YAAY;AAAA,QACd,GAAG,wCALH;AAAA;AAAA;AAAA;AAAA,eAOA;AAAA,QACA,gBAAAA,QAAC,UAAK,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,cAAc;AAAA,UACd,UAAU;AAAA,UACV,YAAY;AAAA,QACd,GACG;AAAA,qBAAW;AAAA,UAAO;AAAA,aARrB;AAAA;AAAA;AAAA;AAAA,eASA;AAAA,WAxBF;AAAA;AAAA;AAAA;AAAA,aAyBA;AAAA,MAEC,WAAW,IAAI,CAAC,UAAU,UACzB,gBAAAA;AAAA,QAAC;AAAA;AAAA,UAEC;AAAA,UACA,UAAU,MAAM,qBAAqB,KAAK;AAAA;AAAA,QAFrC,SAAS;AAAA,QADhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA,CACD;AAAA,SAlCH;AAAA;AAAA;AAAA;AAAA,WAmCA;AAAA,IAID,WAAW,WAAW,KAAK,CAAC,aAC3B,gBAAAA,QAAC,SAAI,OAAO;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GACE;AAAA,sBAAAA,QAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,cAAc,OAAO,GAAG,yBAAxD;AAAA;AAAA;AAAA;AAAA,aAA0D;AAAA,MAC1D,gBAAAA,QAAC,QAAG,OAAO;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,MACd,GAAG,0DALH;AAAA;AAAA;AAAA;AAAA,aAOA;AAAA,MACA,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,KAAK,UAAU,OAAO,GAAG,sHAA7C;AAAA;AAAA;AAAA;AAAA,aAEA;AAAA,SAhBF;AAAA;AAAA;AAAA;AAAA,WAiBA;AAAA,OA/FJ;AAAA;AAAA;AAAA;AAAA,SAiGA;AAEJ;;;AKpLA,IAAO,0BAAQ,EAAC,OAAQ,EAAC,QAAS,mCAAkC,SAAU,CAAC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,kCAAkC,EAAC,GAAE,QAAS,EAAC,MAAO,EAAC,IAAK,QAAO,UAAW,QAAU,MAAO,IAAG,OAAQ,QAAU,eAAgB,QAAU,QAAS,2BAA0B,SAAU,QAAU,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,iBAAgB,EAAC,IAAK,iBAAgB,UAAW,QAAO,MAAO,QAAU,OAAQ,IAAK,eAAgB,QAAU,QAAS,oCAAmC,SAAU,QAAU,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,8BAA6B,EAAC,IAAK,8BAA6B,UAAW,QAAO,MAAO,uBAAsB,OAAQ,QAAU,eAAgB,QAAU,QAAS,iDAAgD,SAAU,QAAU,WAAY,IAAK,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,EAAC,GAAE,SAAU,YAAW,KAAM,EAAC,SAAU,qCAAoC,WAAY,cAAa,GAAE,KAAM,8BAA6B;;;ACK12C,IAAM,OAAO,eAEP,uBAAuB,iBACvB,SAAS,EAAC,mBAAoB,IAAM,sBAAuB,IAAM,qBAAsB,IAAM,gBAAiB,IAAM,gBAAiB,IAAM,uBAAwB,IAAM,uBAAwB,GAAK,GACtM,aAAa,WACb,QAAQ,EAAE,QAAQ,qBAAY,GAC9B,SAAS;AAAA,EACpB,MAAQ;AAAA,IACN,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,8BAA8B;AAAA,IAC1B,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,iBAAiB;AAAA,IACb,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AACF;", "names": ["jsxDEV", "jsxDEV", "jsxDEV", "jsxDEV", "jsxDEV", "error"]}